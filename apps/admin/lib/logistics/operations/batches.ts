import { BaseService } from "../base/service";
import {
  Batch,
  BatchInsert,
  BatchUpdate,
  BatchWithRelations,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  StatusEnum,
} from "../types";

// Lazy imports to avoid circular dependencies
let ledgerService: any = null;
let invoiceService: any = null;

async function getLedgerService() {
  if (!ledgerService) {
    const { ledgerService: service } = await import("./ledgers");
    ledgerService = service;
  }
  return ledgerService;
}

async function getInvoiceService() {
  if (!invoiceService) {
    const { invoiceService: service } = await import("./invoices");
    invoiceService = service;
  }
  return invoiceService;
}

export class BatchService extends BaseService<Batch, BatchInsert, BatchUpdate> {
  protected tableName = "batches";

  // Get batch with all relationships
  async getBatchWithRelations(
    id: string
  ): Promise<ServiceResponse<BatchWithRelations>> {
    try {
      const { data, error } = await this.supabase
        .from("batches")
        .select(
          `
          *,
          cargos (
            id,
            tracking_number,
            particular,
            status,
            weight_value,
            weight_unit
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as any,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get batch with relations",
        success: false,
      };
    }
  }

  // Get all batches with cargo relations
  async getAllBatchesWithCargo(
    params?: QueryParams
  ): Promise<ServiceListResponse<BatchWithRelations>> {
    try {
      let query = this.supabase
        .from("batches")
        .select(
          `
          *,
          cargos (
            id,
            status,
            weight_value,
            cbm_value
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `,
          { count: "exact" }
        )
        .neq("status", "INACTIVE");

      // Apply additional filters
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== "") {
            query = query.eq(key, value);
          }
        });
      }

      // Apply sorting
      if (params?.column && params?.ascending !== undefined) {
        query = query.order(params.column, { ascending: params.ascending });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.page && params?.limit) {
        const from = (params.page - 1) * params.limit;
        const to = from + params.limit - 1;
        query = query.range(from, to);
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          count: 0,
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data as any) || [],
        count: count || 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        error: error.message || "Failed to get batches with cargo",
        success: false,
      };
    }
  }

  // Get available batches (with capacity)
  async getAvailableBatches(
    params?: QueryParams
  ): Promise<ServiceListResponse<BatchWithRelations>> {
    try {
      let query = this.supabase
        .from("batches")
        .select(
          `
          *,
          cargos (
            id,
            weight_value,
            cbm_value
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `,
          { count: "exact" }
        )
        .in("status", ["CREATED", "PROCESSING", "ACTIVE"]);

      // Apply additional filters
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== "") {
            query = query.eq(key, value);
          }
        });
      }

      // Apply sorting
      if (params?.column && params?.ascending !== undefined) {
        query = query.order(params.column, { ascending: params.ascending });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.page && params?.limit) {
        const from = (params.page - 1) * params.limit;
        const to = from + params.limit - 1;
        query = query.range(from, to);
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          count: 0,
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data as any) || [],
        count: count || 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        error: error.message || "Failed to get available batches",
        success: false,
      };
    }
  }

  // Create batch with automatic ledger creation
  async createBatch(data: BatchInsert): Promise<ServiceResponse<Batch>> {
    try {
      // Create the batch first
      const batchResult = await this.create(data);

      if (!batchResult.success || !batchResult.data) {
        return batchResult;
      }

      const batch = batchResult.data;

      // Create associated ledger for financial tracking
      try {
        const ledgerService = await getLedgerService();

        const ledgerData = {
          name: `Batch ${batch.name} (${batch.code}) Ledger`,
          status: "ACTIVE" as StatusEnum,
          tags: ["batch", "logistics"],
          associated_table: "batches",
          associated_id: batch.id,
          account_id: data.account_id,
        };

        await ledgerService.createLedger(ledgerData);
      } catch (ledgerError) {
        // Log ledger creation failure but don't fail batch creation
        console.error("Failed to create ledger for batch:", ledgerError);
      }

      return batchResult;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create batch",
        success: false,
      };
    }
  }

  async deleteBatch(id: string) {
    try {
      await Promise.all([this.delete(id), this.chainDeleteRelatedEntities(id)]);
      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: false,
        error: error.message || "Failed to delete batch",
        success: false,
      };
    }
  }

  /**
   * Chain delete related entities (invoices & ledgers) based on batch_id
   * This method soft deletes all invoices and ledgers associated with the batch
   * @param batchId - The ID of the batch to delete related entities for
   */
  private async chainDeleteRelatedEntities(batchId: string): Promise<void> {
    try {
      await Promise.all([
        this.deleteCargoByBatchId(batchId),
        this.deleteInvoicesByBatchId(batchId),
        this.deleteLedgersByBatchId(batchId),
      ]);

      console.log(`Successfully processed chain deletion for batch ${batchId}`);
    } catch (error) {
      console.error(`Error in chain deletion for batch ${batchId}:`, error);
      // Don't throw error to prevent blocking batch deletion
    }
  }

  /**
   * Unlink all cargo instances assigned to a specific batch ID
   * This method sets batch_id to null for all cargo assigned to the given batch
   * @param batchId - The ID of the batch to unlink cargo from
   * @returns ServiceResponse indicating success or failure
   */
  private async deleteCargoByBatchId(
    batchId: string
  ): Promise<ServiceResponse<number>> {
    try {
      const { data, error } = await this.supabase
        .from("cargos")
        .update({
          batch_id: null,
          updated_at: new Date().toISOString(),
        })
        .eq("batch_id", batchId)
        .select("id");

      if (error) {
        return {
          data: null,
          error: `Failed to unlink cargo from batch ${batchId}: ${error.message}`,
          success: false,
        };
      }

      const unlinkedCount = data ? data.length : 0;

      return {
        data: unlinkedCount,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || `Failed to unlink cargo from batch ${batchId}`,
        success: false,
      };
    }
  }

  /**
   * Delete all invoices associated with a batch ID
   * @param batchId - The ID of the batch
   */
  private async deleteInvoicesByBatchId(batchId: string): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from("invoices")
        .update({
          status: "INACTIVE",
          batch_id: null,
          updated_at: new Date().toISOString(),
        })
        .eq("batch_id", batchId)
        .select("id");

      if (error) {
        console.error(
          `Failed to delete invoices for batch ${batchId}:`,
          error.message
        );
        return;
      }

      const deletedCount = data ? data.length : 0;
      console.log(`Soft deleted ${deletedCount} invoices for batch ${batchId}`);
    } catch (error) {
      console.error(
        `Exception while deleting invoices for batch ${batchId}:`,
        error
      );
    }
  }

  /**
   * Delete all ledgers associated with a batch ID
   * @param batchId - The ID of the batch
   */
  private async deleteLedgersByBatchId(batchId: string): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from("ledgers")
        .update({
          batch_id: null,
          status: "INACTIVE",
          updated_at: new Date().toISOString(),
        })
        .eq("batch_id", batchId)
        .select("id");

      if (error) {
        console.error(
          `Failed to delete ledgers for batch ${batchId}:`,
          error.message
        );
        return;
      }

      const deletedCount = data ? data.length : 0;
      console.log(`Soft deleted ${deletedCount} ledgers for batch ${batchId}`);
    } catch (error) {
      console.error(
        `Exception while deleting ledgers for batch ${batchId}:`,
        error
      );
    }
  }
}

// Create and export service instance
export const batchService = new BatchService();
