import {
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from "@nestjs/common";
import { createClient } from "@/lib/supabase/client";
import {
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  FilterParams,
  StatusEnum,
} from "../types";

export abstract class BaseService<T = any, TInsert = any, TUpdate = any> {
  protected supabase = createClient();
  protected abstract tableName: string;

  // Create a new record
  async create(data: TInsert): Promise<ServiceResponse<T>> {
    try {
      const { data: result, error } = await this.supabase
        .from(this.tableName as any)
        .insert(data as any)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: result as T,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || `Failed to create ${this.tableName}`,
        success: false,
      };
    }
  }

  // Get a record by ID
  async getById(id: string, select?: string): Promise<ServiceResponse<T>> {
    try {
      const query = this.supabase
        .from(this.tableName as any)
        .select(select || "*")
        .eq("id", id)
        .single();

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as T,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || `Failed to get ${this.tableName}`,
        success: false,
      };
    }
  }

  // Get all records with optional filtering, pagination, and sorting
  async getAll(
    params?: QueryParams,
    select?: string,
    includeInactive: boolean = false
  ): Promise<ServiceListResponse<T>> {
    try {
      let query = this.supabase
        .from(this.tableName as any)
        .select(select || "*", { count: "exact" })
        .neq("status", "INACTIVE");

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        // Default sort by created_at descending
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data as T[]) || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || `Failed to get ${this.tableName} list`,
        success: false,
      };
    }
  }

  // Update a record by ID
  async update(id: string, data: TUpdate): Promise<ServiceResponse<T>> {
    try {
      console.log(`[${this.tableName}] Updating record with id: ${id}`, data);

      // First check if the record exists
      const { data: existingRecord, error: checkError } = await this.supabase
        .from(this.tableName as any)
        .select("id")
        .eq("id", id)
        .single();

      if (checkError) {
        console.error(`[${this.tableName}] Record check failed:`, checkError);
        if (checkError.code === "PGRST116") {
          return {
            data: null,
            error: `Record with id ${id} not found in ${this.tableName}`,
            success: false,
          };
        }
        return {
          data: null,
          error: checkError.message,
          success: false,
        };
      }

      console.log(`[${this.tableName}] Record exists, proceeding with update`);

      // Add updated_at timestamp to the update data
      const updateData = {
        ...data,
        updated_at: new Date().toISOString(),
      };

      // Now perform the update
      const { data: result, error } = await this.supabase
        .from(this.tableName as any)
        .update(updateData as any)
        .eq("id", id)
        .select();

      if (error) {
        console.error(`[${this.tableName}] Update failed:`, error);
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      // Check if any rows were updated
      if (!result || result.length === 0) {
        console.warn(`[${this.tableName}] No rows updated for id: ${id}`);
        return {
          data: null,
          error: `No rows were updated for id ${id} in ${this.tableName}`,
          success: false,
        };
      }

      console.log(`[${this.tableName}] Update successful:`, result[0]);

      // Return the first (and should be only) updated record
      return {
        data: result[0] as T,
        error: null,
        success: true,
      };
    } catch (error: any) {
      console.error(`[${this.tableName}] Update exception:`, error);
      return {
        data: null,
        error: error.message || `Failed to update ${this.tableName}`,
        success: false,
      };
    }
  }

  // Delete a record by ID (soft delete by setting status to INACTIVE)
  async delete(
    id: string,
    hardDelete: boolean = false
  ): Promise<ServiceResponse<boolean>> {
    try {
      // First check if the record exists
      const { data: existingRecord, error: checkError } = await this.supabase
        .from(this.tableName as any)
        .select("id")
        .eq("id", id)
        .single();

      if (checkError) {
        if (checkError.code === "PGRST116") {
          return {
            data: null,
            error: `Record with id ${id} not found in ${this.tableName}`,
            success: false,
          };
        }
        return {
          data: null,
          error: checkError.message,
          success: false,
        };
      }

      if (hardDelete) {
        const { error } = await this.supabase
          .from(this.tableName as any)
          .delete()
          .eq("id", id);

        if (error) {
          return {
            data: null,
            error: error.message,
            success: false,
          };
        }
      } else {
        // Soft delete - set status to INACTIVE with updated_at timestamp
        const { data: result, error } = await this.supabase
          .from(this.tableName as any)
          .update({
            status: "INACTIVE",
            updated_at: new Date().toISOString(),
          } as any)
          .eq("id", id)
          .select();

        if (error) {
          return {
            data: null,
            error: error.message,
            success: false,
          };
        }

        // Check if any rows were updated
        if (!result || result.length === 0) {
          return {
            data: null,
            error: `No rows were updated for id ${id} in ${this.tableName}`,
            success: false,
          };
        }
      }

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || `Failed to delete ${this.tableName}`,
        success: false,
      };
    }
  }

  // Update status of a record
  async updateStatus(
    id: string,
    status: StatusEnum
  ): Promise<ServiceResponse<T>> {
    try {
      // First check if the record exists
      const { data: existingRecord, error: checkError } = await this.supabase
        .from(this.tableName as any)
        .select("id")
        .eq("id", id)
        .single();

      if (checkError) {
        if (checkError.code === "PGRST116") {
          return {
            data: null,
            error: `Record with id ${id} not found in ${this.tableName}`,
            success: false,
          };
        }
        return {
          data: null,
          error: checkError.message,
          success: false,
        };
      }

      // Add updated_at timestamp to the status update
      const updateData = {
        status,
        updated_at: new Date().toISOString(),
      };

      // Now perform the status update
      const { data: result, error } = await this.supabase
        .from(this.tableName as any)
        .update(updateData as any)
        .eq("id", id)
        .select();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      // Check if any rows were updated
      if (!result || result.length === 0) {
        return {
          data: null,
          error: `No rows were updated for id ${id} in ${this.tableName}`,
          success: false,
        };
      }

      // Return the first (and should be only) updated record
      return {
        data: result[0] as T,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || `Failed to update ${this.tableName} status`,
        success: false,
      };
    }
  }

  // Count records with optional filters
  async count(
    filters?: FilterParams,
    includeInactive: boolean = false
  ): Promise<ServiceResponse<number>> {
    try {
      let query = this.supabase
        .from(this.tableName as any)
        .select("*", { count: "exact", head: true });

      // Apply default status filtering to exclude INACTIVE items unless explicitly requested
      if (!includeInactive) {
        query = query.neq("status", "INACTIVE");
      }

      if (filters) {
        query = this.applyFilters(query, filters);
      }

      const { count, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: count || 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || `Failed to count ${this.tableName}`,
        success: false,
      };
    }
  }

  // Search records by text
  async search(
    searchTerm: string,
    columns: string[],
    params?: QueryParams,
    includeInactive: boolean = false
  ): Promise<ServiceListResponse<T>> {
    try {
      let query = this.supabase
        .from(this.tableName as any)
        .select("*", { count: "exact" });

      // Apply default status filtering to exclude INACTIVE items unless explicitly requested
      if (!includeInactive) {
        query = query.neq("status", "INACTIVE");
      }

      // Add text search conditions
      const searchConditions = columns
        .map((column) => `${column}.ilike.%${searchTerm}%`)
        .join(",");
      query = query.or(searchConditions);

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data as T[]) || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || `Failed to search ${this.tableName}`,
        success: false,
      };
    }
  }

  // Apply filters to a query
  protected applyFilters(query: any, filters: FilterParams, filter?: string) {
    Object.entries(filters).forEach(([key, value]) => {
      if (
        value !== undefined &&
        value !== null &&
        value !== "" &&
        key !== "query"
      ) {
        if (key === "status") {
          if (Array.isArray(value)) {
            query = query.in("status", value);
          } else {
            query = query.eq("status", value);
          }
        } else if (key === "search" && typeof value === "string") {
          // Skip search here as it's handled in search method
          return;
        } else if (key === "dateFrom") {
          query = query.gte("created_at", value);
        } else if (key === "dateTo") {
          query = query.lte("created_at", value);
        } else if (Array.isArray(value)) {
          query = query.in(key, value);
        } else {
          query = query[filter || "eq"](key, value);
        }
      }
    });
    return query;
  }

  // Bulk operations
  async bulkCreate(data: TInsert[]): Promise<ServiceListResponse<T>> {
    try {
      const { data: result, error } = await this.supabase
        .from(this.tableName as any)
        .insert(data as any)
        .select();

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: (result as T[]) || [],
        error: null,
        success: true,
        count: result?.length || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || `Failed to bulk create ${this.tableName}`,
        success: false,
      };
    }
  }

  async bulkUpdate(
    updates: Array<{ id: string; data: TUpdate }>
  ): Promise<ServiceResponse<boolean>> {
    try {
      // Since Supabase doesn't support bulk updates with different data,
      // we'll do them individually in a transaction-like manner
      const promises = updates.map(({ id, data }) => {
        const updateData = {
          ...data,
          updated_at: new Date().toISOString(),
        };
        return this.supabase
          .from(this.tableName as any)
          .update(updateData as any)
          .eq("id", id);
      });

      const results = await Promise.all(promises);
      const hasError = results.some((result) => result.error);

      if (hasError) {
        const errors = results
          .filter((r) => r.error)
          .map((r) => r.error!.message);
        return {
          data: null,
          error: `Bulk update failed: ${errors.join(", ")}`,
          success: false,
        };
      }

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || `Failed to bulk update ${this.tableName}`,
        success: false,
      };
    }
  }

  // Get records by foreign key
  async getByForeignKey(
    column: string,
    value: string,
    params?: QueryParams,
    includeInactive: boolean = false
  ): Promise<ServiceListResponse<T>> {
    try {
      let query = this.supabase
        .from(this.tableName as any)
        .select("*", { count: "exact" })
        .eq(column, value)
        .neq("status", "INACTIVE");

      // Apply default status filtering to exclude INACTIVE items unless explicitly requested
      if (!includeInactive) {
        query = query.neq("status", "INACTIVE");
      }

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data as T[]) || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || `Failed to get ${this.tableName} by ${column}`,
        success: false,
      };
    }
  }

  /**
   * Apply smart ordering: created_at desc first, then updated_at desc
   * This method can be overridden by child services for custom ordering logic
   */
  protected applyDefaultOrdering(query: any, params?: any): any {
    if (params?.column) {
      // If explicit column is provided, use it
      return query.order(params.column, {
        ascending: params.ascending ?? false,
      });
    } else {
      // Smart ordering: created_at first, then updated_at
      // Note: We'll use a compound ordering to handle cases where updated_at might be null
      return query
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false });
    }
  }

  handleError(error: any, defaultMessage: string): never {
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }
    throw new InternalServerErrorException(
      defaultMessage + (error.message ? `: ${error.message}` : "")
    );
  }
}
