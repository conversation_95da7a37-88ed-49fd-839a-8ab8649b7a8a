import { type CargoWithRelations } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";

// Types for display data
export interface CargoDisplay {
  id: string;
  trackingNumber: string;
  chinaTrackingNumber?: string;
  type: string;
  origin: string;
  destination: string;
  status: string;
  weight: string;
  cbm: string;
  ctn: number;
  dimensions: string;
  unitPrice: string;
  totalPrice: string;
  invoiceStatus: string;
  customer: string;
  customerId: string;
  customerPhone?: string;
  customDeclaration: boolean;
  entityType: "customer" | "supplier" | "unknown";
  date: string;
  updatedAt: string;
  particular?: string;
  batchCode?: string;
  batchId?: string;
  assignedTo?: string;
  assignedToId?: string;
}

export interface CargoStats {
  totalCargos: number;
  inTransit: number;
  delivered: number;
  issues: number;
  totalValue: number;
  totalCBM: number;
  totalCTN: number;
  totalWeight: number;
  totalCustomers: number;
}

export interface CargoManagementState {
  // View state
  viewMode: "cards" | "table";
  searchTerm: string;
  categoryFilter: string;
  currentPage: number;
  loading: boolean;
  refreshing: boolean;

  // Data state
  cargoData: CargoDisplay[];
  cargoStats: CargoStats;

  // Dialog state
  isNewCargoOpen: boolean;
  qrCodeDialog: {
    open: boolean;
    cargo: CargoDisplay | null;
  };
  cargoDetailsDialog: {
    open: boolean;
    cargo: CargoDisplay | null;
  };

  // Filter state
  columnFilters: ColumnFilter[];
  filters: {
    cargoType: string;
    status: string;
    dateFrom: string;
    dateTo: string;
  };

  // Bulk actions state
  selectedCargos: Set<string>;
  bulkActionLoading: boolean;
  bulkStatusUpdate: string;

  // Status update states
  updatingStatuses: Record<string, boolean>;

  // Demo state
  demoMode: boolean;

  // Export dialog state
  exportDialog: {
    isOpen: boolean;
    data: CargoDisplay[];
    config: {
      title?: string;
      filename?: string;
      excludeColumns?: string[];
      columnMapping?: Record<string, string>;
    };
  };

  // Bulk task creation state
  isBulkTaskDialogOpen: boolean;
}

export const ITEMS_PER_PAGE = 10;
