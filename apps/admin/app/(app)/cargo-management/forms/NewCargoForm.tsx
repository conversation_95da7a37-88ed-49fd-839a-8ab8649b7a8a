"use client";

import { memo, useState, useEffect, useCallback } from "react";
import { Loader2, Upload, FileText, X, Plus } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Label } from "@workspace/ui/components/label";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@workspace/ui/components/combobox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { DialogFooter, DialogClose } from "@workspace/ui/components/dialog";
import { AnimatedTabGroup } from "@/components/animated-tab-group";
import { useAppSelector } from "@/store/hooks";
import {
  cargoService,
  customerService,
  supplierService,
  invoiceService,
  batchService,
  userService,
  type Customer,
  type Supplier,
  type InvoiceWithCustomer,
  type BatchWithRelations,
  type UserWithAccount,
  Cargo_Categories,
  CARGO_CATEGORY_LABELS,
  CargoWithRelations,
} from "@/lib/logistics";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";
import { documentService } from "@/lib/logistics/operations/documents";
import { toast } from "sonner";
import { InputField, SelectField } from "./index";

interface NewCargoFormProps {
  batchId?: string;
  withBatchSelect?: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

/**
 * New Cargo Form Component
 *
 * Form for creating new cargo items with customer/supplier selection,
 * dimensions, weight, pricing, and invoice management.
 * Memoized to prevent unnecessary re-renders.
 */
export const NewCargoForm = memo<NewCargoFormProps>(
  ({ batchId, withBatchSelect = true, onOpenChange, onSuccess }) => {
    // State variables
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [customers, setCustomers] = useState<Customer[]>([]);
    const [suppliers, setSuppliers] = useState<Supplier[]>([]);
    const [invoices, setInvoices] = useState<InvoiceWithCustomer[]>([]);
    const [batches, setBatches] = useState<BatchWithRelations[]>([]);
    const [staff, setStaff] = useState<UserWithAccount[]>([]);
    const [loadingCustomers, setLoadingCustomers] = useState(true);
    const [loadingSuppliers, setLoadingSuppliers] = useState(true);
    const [loadingInvoices, setLoadingInvoices] = useState(false);
    const [loadingBatches, setLoadingBatches] = useState(true);
    const [loadingStaff, setLoadingStaff] = useState(true);
    const [recentPaidInvoice, setRecentPaidInvoice] =
      useState<InvoiceWithCustomer | null>(null);
    const [loadingRecentInvoice, setLoadingRecentInvoice] = useState(false);
    const [factor, setFactor] = useState("Weight");
    const [entityType, setEntityType] = useState<"customer" | "supplier">(
      "customer"
    );

    // Combobox states
    const [customerComboboxOpen, setCustomerComboboxOpen] = useState(false);
    const [supplierComboboxOpen, setSupplierComboboxOpen] = useState(false);
    const [assigneeComboboxOpen, setAssigneeComboboxOpen] = useState(false);
    const [batchComboboxOpen, setBatchComboboxOpen] = useState(false);

    // Document attachment state
    const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

    const INITIAL_FORM_DATA = {
      type: "",
      customer_id: "",
      supplier_id: "",
      particular: "",
      china_tracking_number: "",
      batch_id: batchId || "none",
      assigned_to: "", // Staff member assigned to handle this cargo
      quantity: 1,
      dimensionLength: 0.0,
      dimensionWidth: 0.0,
      dimensionHeight: 0.0,
      weightValue: 0.0,
      weightUnit: "KILOGRAMS",
      totalPrice: 0.0,
      CTN: 0,
      unit_price: 0.0,
      invoice_id: "",
      factor_unit: "",
      factor_value: 1,
      custom_declaration: false,
    };

    const [formData, setFormData] = useState(INITIAL_FORM_DATA);

    // Get authenticated user
    const { user: authUser } = useAppSelector((state) => state.auth);

    const fetchData = async () => {
      try {
        // Fetch customers
        const customersResult = await customerService.getActiveCustomers();
        if (customersResult.success) {
          setCustomers(customersResult.data);
        }
        setLoadingCustomers(false);

        // Fetch suppliers
        const suppliersResult = await supplierService.getActiveSuppliers();
        if (suppliersResult.success) {
          setSuppliers(suppliersResult.data);
        }
        setLoadingSuppliers(false);

        // Fetch available batches
        let filters: any = { id: batchId ?? null };
        const batchesResult = await batchService.getAvailableBatches({
          filters,
        });
        if (batchesResult.success) {
          setBatches(batchesResult.data);
        }
        setLoadingBatches(false);

        // Fetch staff members for assignment
        const staffResult = await userService.getUsersForAssignment([]);
        if (staffResult.success) {
          setStaff(staffResult.data);
        }
        setLoadingStaff(false);
      } catch (error) {
        console.error("Error fetching form data:", error);
        setLoadingCustomers(false);
        setLoadingSuppliers(false);
        setLoadingBatches(false);
        setLoadingStaff(false);
      }
    };

    // Fetch customers and suppliers for dropdown
    useEffect(() => {
      fetchData();
    }, []);

    // Fetch invoices when customer is selected
    useEffect(() => {
      const fetchInvoices = async () => {
        if (entityType !== "customer" || !formData.customer_id) {
          setInvoices([]);
          return;
        }

        try {
          setLoadingInvoices(true);
          const invoicesResult =
            await invoiceService.getAllInvoicesWithCustomers({
              limit: 50,
            });

          if (invoicesResult.success && invoicesResult.data) {
            const customerInvoices = invoicesResult.data.filter(
              (invoice) => invoice.customer_id === formData.customer_id
            );
            setInvoices(customerInvoices);
          }
        } catch (error) {
          console.error("Error fetching invoices:", error);
        } finally {
          setLoadingInvoices(false);
        }
      };

      fetchInvoices();
    }, [formData.customer_id, entityType]);

    // Fetch most recent paid invoice when customer is selected
    useEffect(() => {
      const fetchRecentPaidInvoice = async () => {
        if (entityType !== "customer" || !formData.customer_id) {
          setRecentPaidInvoice(null);
          return;
        }

        try {
          setLoadingRecentInvoice(true);
          const invoicesResult =
            await invoiceService.getAllInvoicesWithCustomers({
              limit: 100, // Get enough to find recent paid ones
            });

          if (invoicesResult.success && invoicesResult.data) {
            // Filter for this customer's paid invoices and get the most recent
            const customerPaidInvoices = invoicesResult.data
              .filter(
                (invoice) =>
                  invoice.customer_id === formData.customer_id &&
                  invoice.status === "PAID"
              )
              .sort(
                (a, b) =>
                  new Date(b.created_at).getTime() -
                  new Date(a.created_at).getTime()
              );

            if (customerPaidInvoices.length > 0) {
              setRecentPaidInvoice(customerPaidInvoices[0] || null);
            } else {
              setRecentPaidInvoice(null);
            }
          }
        } catch (error) {
          console.error("Error fetching recent paid invoice:", error);
          setRecentPaidInvoice(null);
        } finally {
          setLoadingRecentInvoice(false);
        }
      };

      fetchRecentPaidInvoice();
    }, [formData.customer_id, entityType]);

    const handleInputChange = (field: string, value: string) => {
      if (field === "custom_declaration") {
        setFormData((prev) => ({ ...prev, [field]: value === "true" }));
      } else {
        setFormData((prev) => ({ ...prev, [field]: value }));
      }
    };

    const handleEntityTypeChange = (type: "customer" | "supplier") => {
      setEntityType(type);
      if (type === "customer") {
        setFormData((prev) => ({ ...prev, supplier_id: "" }));
      } else {
        setFormData((prev) => ({ ...prev, customer_id: "", invoice_id: "" }));
        setInvoices([]);
        setRecentPaidInvoice(null); // Clear recent paid invoice when switching to supplier
      }
    };

    function calculateCBM() {
      const { dimensionLength, dimensionWidth, dimensionHeight, CTN } =
        formData ?? {};
      let cbm = dimensionLength * dimensionWidth * dimensionHeight * CTN;
      return cbm / 1000000 || 0;
    }

    function calculateTotalPrice() {
      const { unit_price, weightValue, quantity } = formData ?? {};
      let CBM: number = calculateCBM();
      let weight: number = weightValue;
      let unitPrice: number = unit_price;
      let qty: number = quantity || 1;
      let factor = weight || CBM;

      if (weight) {
        setFactor("Weight");
        factor = weight;
      } else if (CBM) {
        setFactor("CBM");
        factor = CBM;
      }

      let totalPrice = unitPrice * factor * qty;
      return totalPrice;
    }

    useEffect(() => {
      setFormData((prev) => ({
        ...prev,
        totalPrice: calculateTotalPrice(),
      }));
    }, [
      formData.unit_price,
      formData.weightValue,
      formData.dimensionLength,
      formData.dimensionWidth,
      formData.dimensionHeight,
      formData.quantity,
      formData.CTN,
    ]);

    // File upload handlers
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      addFiles(files);
      // Reset input
      event.target.value = "";
    };

    const addFiles = (files: File[]) => {
      // Filter out files that are too large (10MB limit)
      const validFiles = files.filter((file) => {
        if (file.size > 10 * 1024 * 1024) {
          toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
          return false;
        }
        return true;
      });

      setAttachedFiles((prev) => [...prev, ...validFiles]);
    };

    const removeAttachedFile = (index: number) => {
      setAttachedFiles((prev) => prev.filter((_, i) => i !== index));
    };

    // Drag and drop handlers
    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDragEnter = (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDragLeave = (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const files = Array.from(e.dataTransfer.files);
      addFiles(files);
    };

    // Get file type icon
    const getFileTypeIcon = (fileName: string) => {
      const extension = fileName.split(".").pop()?.toLowerCase();
      switch (extension) {
        case "pdf":
          return <FileText className="h-4 w-4 text-red-500" />;
        case "doc":
        case "docx":
          return <FileText className="h-4 w-4 text-blue-500" />;
        case "xls":
        case "xlsx":
          return <FileText className="h-4 w-4 text-green-500" />;
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
          return <FileText className="h-4 w-4 text-purple-500" />;
        default:
          return <FileText className="h-4 w-4 text-gray-500" />;
      }
    };

    const checkCBMCapacity = useCallback(
      (cargo: CargoWithRelations): boolean => {
        const cargoCBM = cargo.cbm_value || 0;
        return cbmMetrics.usedCBM + cargoCBM > cbmMetrics.totalCapacity;
      },
      [cbmMetrics]
    );

    const handleSubmit = async () => {
      if (!authUser) return;

      setIsSubmitting(true);
      try {
        // Generate cargo tracking number using centralized service
        // First, get the batch code if batch is assigned
        let batchCode = "";
        if (formData.batch_id) {
          const selectedBatch = batches.find((b) => b.id === formData.batch_id);
          batchCode = selectedBatch?.code || "";
        }

        // Fetch existing cargo tracking numbers for sequential generation
        const existingCargosResult =
          await cargoService.getAllCargosWithRelations({
            filters: {},
            page: 1,
            limit: 1000, // Get all to check tracking numbers
          });

        const existingTrackingNumbers =
          existingCargosResult.success && existingCargosResult.data
            ? existingCargosResult.data
                .map((cargo) => cargo.tracking_number)
                .filter(
                  (trackingNumber): trackingNumber is string => !!trackingNumber
                )
                .filter((trackingNumber) =>
                  trackingNumber.startsWith(`CG/${batchCode || "GENERAL"}/`)
                )
            : [];

        const trackingNumber =
          codeGeneratorService.generateSequentialCargoTrackingNumber(
            {
              batchCode: batchCode || "GENERAL", // Use batch code or fallback
            },
            existingTrackingNumbers
          );

        // Validation
        if (entityType === "customer" && !formData.customer_id) {
          toast.error("Please select a customer.");
          setIsSubmitting(false);
          return;
        }

        if (entityType === "supplier" && !formData.supplier_id) {
          toast.error("Please select a supplier.");
          setIsSubmitting(false);
          return;
        }

        let cbm_value = calculateCBM();
        let weightValue = formData.weightValue;
        let weightUnit = formData.weightUnit;

        if (!weightValue) {
          weightUnit = "KILOGRAMS";
          weightValue = cbm_value * 167;
        }

        // Prepare cargo data
        const cargoData = {
          tracking_number: trackingNumber,
          type: formData.type,
          customer_id: entityType === "customer" ? formData.customer_id : null,
          supplier_id: entityType === "supplier" ? formData.supplier_id : null,
          batch_id:
            formData.batch_id === "none" ? null : formData.batch_id || null,
          assigned_to:
            formData.assigned_to === "none"
              ? null
              : formData.assigned_to || null, // Staff member assignment
          particular: formData.particular || null,
          china_tracking_number: formData.china_tracking_number || null,
          custom_declaration: formData.custom_declaration || false,
          quantity: formData.quantity || 1,
          dimension_length: formData.dimensionLength
            ? formData.dimensionLength
            : null,
          dimension_width: formData.dimensionWidth
            ? formData.dimensionWidth
            : null,
          dimension_height: formData.dimensionHeight
            ? formData.dimensionHeight
            : null,
          dimension_unit: "METERS" as any,
          weight_value: weightValue,
          weight_unit: weightUnit as any,
          factor_unit: factor,
          factor_value: factor === "Weight" ? formData.weightValue : cbm_value,
          total_price: formData.totalPrice ? formData.totalPrice : null,
          unit_price: formData.unit_price ? formData.unit_price : null,
          ctn: formData.CTN,
          cbm_value: cbm_value,
          cbm_unit: "METER_CUBIC" as any,
          account_id: authUser.accountId,
          invoice_id: formData.invoice_id || undefined,
        };

        // First create the cargo without invoice association
        const { invoice_id, ...cargoDataWithoutInvoice } = cargoData;
        const result = await cargoService.createCargo(cargoData);

        if (result.success && result.data) {
          const createdCargo = result.data;
          const entityName =
            entityType === "customer" ? "customer" : "supplier";

          // Get batch info for success message
          const selectedBatch = formData.batch_id
            ? batches.find((batch) => batch.id === formData.batch_id)
            : null;
          const batchInfo = selectedBatch
            ? ` and assigned to batch ${selectedBatch.name} (${selectedBatch.code})`
            : "";

          // Upload documents if any are provided
          if (attachedFiles.length > 0 && authUser?.accountId) {
            let uploadedCount = 0;
            let failedCount = 0;

            for (const file of attachedFiles) {
              try {
                // Upload file to storage
                const uploadResult = await documentService.uploadToStorage({
                  content: file,
                  fileName: file.name,
                  contentType: file.type,
                  folder: "cargo-documents",
                  metadata: {
                    cargoId: createdCargo.id,
                    trackingNumber: createdCargo.tracking_number,
                  },
                });

                if (uploadResult.success && uploadResult.data) {
                  // Create document record in database
                  const documentResult = await documentService.createDocument({
                    name: file.name,
                    path: uploadResult.data,
                    account_id: authUser.accountId,
                    associated_table: "cargos",
                    associated_id: createdCargo.id,
                    category: "cargo-document",
                    description: `Document attached to cargo: ${createdCargo.tracking_number}`,
                    status: "ACTIVE" as any,
                    details: {
                      fileSize: file.size,
                      fileType: file.type,
                      cargoId: createdCargo.id,
                      trackingNumber: createdCargo.tracking_number,
                    },
                  });

                  if (documentResult.success) {
                    uploadedCount++;
                  } else {
                    failedCount++;
                    console.error(
                      "Failed to create document record:",
                      documentResult.error
                    );
                  }
                } else {
                  failedCount++;
                  console.error("Failed to upload file:", uploadResult.error);
                }
              } catch (error) {
                failedCount++;
                console.error("Error uploading file:", error);
              }
            }

            if (failedCount > 0) {
              toast.warning(
                `Cargo created successfully. ${uploadedCount} document(s) uploaded, ${failedCount} failed.`
              );
            } else if (uploadedCount > 0) {
              toast.success(
                `Cargo created successfully with ${uploadedCount} document(s)`
              );
            }
          }

          // Handle invoice association for customers using handleInvoiceUpsert
          if (entityType === "customer" && formData.invoice_id) {
            try {
              await cargoService.handleInvoiceUpsert(
                {
                  ...cargoDataWithoutInvoice,
                  tracking_number: createdCargo.tracking_number,
                  invoice_id: formData.invoice_id,
                },
                formData.customer_id || "",
                "",
                createdCargo.id
              );

              const selectedInvoice = invoices.find(
                (inv) => inv.id === formData.invoice_id
              );
              toast.success("Cargo Created Successfully", {
                description: `Cargo has been created for ${entityName}${batchInfo} and added as a line item to Invoice ${selectedInvoice?.inv_number || formData.invoice_id}. Invoice total has been updated.`,
              });
            } catch (error) {
              toast.warning("Cargo Created with Invoice Warning", {
                description: `Cargo created successfully${batchInfo}, but failed to handle invoice. Please manage invoice manually.`,
              });
            }
          } else {
            toast.success("Cargo Created Successfully", {
              description: `Cargo has been created for ${entityName}${batchInfo}.`,
            });
          }

          onSuccess();
          setEntityType("customer");
          setAttachedFiles([]); // Reset attached files
        } else {
          toast.error("Error Creating Cargo", {
            description:
              result.error || "Failed to create cargo. Please try again.",
          });
        }
      } catch (error) {
        console.error("Error creating cargo:", error);
        toast.error("Failed to Create Cargo", {
          description: "An unexpected error occurred. Please try again.",
        });
      } finally {
        setIsSubmitting(false);
      }
    };

    async function handleAddAnother(event: React.FormEvent) {
      event.preventDefault();
      try {
        await handleSubmit();
      } catch (error: any) {
        toast.error(error.message);
      } finally {
        // Reset form data except for customer_id
        let { customer_id, batch_id, ...rest } = INITIAL_FORM_DATA;
        setFormData({
          ...rest,
          customer_id: formData.customer_id,
          batch_id: formData.batch_id,
        });
      }
    }

    async function handleCreateCargo(event: React.FormEvent) {
      event.preventDefault();
      try {
        await handleSubmit();
      } catch (error: any) {
        toast.error(error.message);
      } finally {
        setFormData(INITIAL_FORM_DATA);
        onOpenChange(false);
      }
    }

    return (
      <form
        id="form"
        onSubmit={handleCreateCargo}
        className="w-full grid gap-5 py-4"
      >
        {/* Entity Type Selection */}
        <div className="mb-3">
          <AnimatedTabGroup
            tabs={[
              {
                id: "customer",
                label: "Customer",
                content: (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1.5">
                      Customer{" "}
                      {loadingCustomers && (
                        <Loader2
                          size={12}
                          className="inline animate-spin ml-1"
                        />
                      )}
                    </label>
                    <Combobox
                      open={customerComboboxOpen}
                      onOpenChange={setCustomerComboboxOpen}
                    >
                      <ComboboxTrigger
                        placeholder="Select Customer"
                        disabled={loadingCustomers}
                      >
                        {formData.customer_id
                          ? (() => {
                              const selectedCustomer = customers.find(
                                (customer) =>
                                  customer.id === formData.customer_id
                              );
                              return selectedCustomer
                                ? `${selectedCustomer.name}${
                                    selectedCustomer.email
                                      ? ` (${selectedCustomer.email})`
                                      : ""
                                  }`
                                : "Select Customer";
                            })()
                          : "Select Customer"}
                      </ComboboxTrigger>
                      <ComboboxContent>
                        <ComboboxCommand>
                          <ComboboxInput placeholder="Search customers..." />
                          <ComboboxList>
                            {loadingCustomers ? (
                              <div className="flex items-center justify-center p-2">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading customers...
                              </div>
                            ) : customers.length === 0 ? (
                              <ComboboxEmpty>No customers found.</ComboboxEmpty>
                            ) : (
                              <ComboboxGroup>
                                {customers.map((customer) => {
                                  const searchableValue =
                                    `${customer.name} ${customer.email || ""}`.trim();
                                  return (
                                    <ComboboxItem
                                      key={customer.id}
                                      value={searchableValue}
                                      onSelect={() => {
                                        const newValue =
                                          formData.customer_id === customer.id
                                            ? ""
                                            : customer.id;
                                        handleInputChange(
                                          "customer_id",
                                          newValue
                                        );
                                        setCustomerComboboxOpen(false);
                                      }}
                                    >
                                      {customer.name}{" "}
                                      {customer.email && `(${customer.email})`}
                                      <ComboboxItemIndicator
                                        isSelected={
                                          formData.customer_id === customer.id
                                        }
                                      />
                                    </ComboboxItem>
                                  );
                                })}
                              </ComboboxGroup>
                            )}
                          </ComboboxList>
                        </ComboboxCommand>
                      </ComboboxContent>
                    </Combobox>
                  </div>
                ),
              },
              {
                id: "supplier",
                label: "Supplier",
                content: (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1.5">
                      Supplier{" "}
                      {loadingSuppliers && (
                        <Loader2
                          size={12}
                          className="inline animate-spin ml-1"
                        />
                      )}
                    </label>
                    <Combobox
                      open={supplierComboboxOpen}
                      onOpenChange={setSupplierComboboxOpen}
                    >
                      <ComboboxTrigger
                        placeholder="Select Supplier"
                        disabled={loadingSuppliers}
                      >
                        {formData.supplier_id
                          ? (() => {
                              const selectedSupplier = suppliers.find(
                                (supplier) =>
                                  supplier.id === formData.supplier_id
                              );
                              return selectedSupplier
                                ? `${
                                    selectedSupplier.tracking_number ||
                                    selectedSupplier.phone ||
                                    selectedSupplier.location ||
                                    `Supplier ${selectedSupplier.id}`
                                  }${
                                    selectedSupplier.phone
                                      ? ` (${selectedSupplier.phone})`
                                      : ""
                                  }`
                                : "Select Supplier";
                            })()
                          : "Select Supplier"}
                      </ComboboxTrigger>
                      <ComboboxContent>
                        <ComboboxCommand>
                          <ComboboxInput placeholder="Search suppliers..." />
                          <ComboboxList>
                            {loadingSuppliers ? (
                              <div className="flex items-center justify-center p-2">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading suppliers...
                              </div>
                            ) : suppliers.length === 0 ? (
                              <ComboboxEmpty>No suppliers found.</ComboboxEmpty>
                            ) : (
                              <ComboboxGroup>
                                {suppliers.map((supplier) => {
                                  const displayName =
                                    supplier.tracking_number ||
                                    supplier.phone ||
                                    supplier.location ||
                                    `Supplier ${supplier.id}`;
                                  const searchableValue =
                                    `${displayName} ${supplier.phone || ""} ${supplier.location || ""}`.trim();
                                  return (
                                    <ComboboxItem
                                      key={supplier.id}
                                      value={searchableValue}
                                      onSelect={() => {
                                        const newValue =
                                          formData.supplier_id === supplier.id
                                            ? ""
                                            : supplier.id;
                                        handleInputChange(
                                          "supplier_id",
                                          newValue
                                        );
                                        setSupplierComboboxOpen(false);
                                      }}
                                    >
                                      {displayName}
                                      {supplier.phone && ` (${supplier.phone})`}
                                      <ComboboxItemIndicator
                                        isSelected={
                                          formData.supplier_id === supplier.id
                                        }
                                      />
                                    </ComboboxItem>
                                  );
                                })}
                              </ComboboxGroup>
                            )}
                          </ComboboxList>
                        </ComboboxCommand>
                      </ComboboxContent>
                    </Combobox>
                  </div>
                ),
              },
            ]}
            defaultTabId={entityType}
            onTabChange={(tabId: string) =>
              handleEntityTypeChange(tabId as "customer" | "supplier")
            }
            className="border border-gray-200 rounded-lg bg-gray-50 p-2"
          />
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <InputField
            id="description"
            label="Cargo Description"
            placeholder="e.g: Electronics, 10 pallets"
            value={formData.particular}
            onChange={(e) => handleInputChange("particular", e.target.value)}
          />
          <SelectField
            id="cargoType"
            label="Cargo Type"
            placeholder="Select Cargo Type"
            value={formData.type}
            options={[
              {
                value: Cargo_Categories.DANGEROUS,
                label: CARGO_CATEGORY_LABELS[Cargo_Categories.DANGEROUS],
              },
              {
                value: Cargo_Categories.SAFE,
                label: CARGO_CATEGORY_LABELS[Cargo_Categories.SAFE],
              },
              {
                value: Cargo_Categories.COPY,
                label: CARGO_CATEGORY_LABELS[Cargo_Categories.COPY],
              },
            ]}
            onChange={(value) => handleInputChange("type", value)}
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <InputField
            id="china_tracking_number"
            label="China Tracking Number"
            placeholder="e.g: Previous tracking reference (optional)"
            value={formData.china_tracking_number}
            onChange={(e) =>
              handleInputChange("china_tracking_number", e.target.value)
            }
          />
          <div hidden={!withBatchSelect}>
            <label className="block text-sm font-medium text-gray-700 mb-1.5">
              Batch Assignment (Optional){" "}
              {loadingBatches && (
                <Loader2 size={12} className="inline animate-spin ml-1" />
              )}
            </label>
            <Combobox
              open={batchComboboxOpen}
              onOpenChange={setBatchComboboxOpen}
            >
              <ComboboxTrigger
                placeholder="Select batch (optional)"
                disabled={loadingBatches}
              >
                {formData.batch_id && formData.batch_id !== "none"
                  ? (() => {
                      const selectedBatch = batches.find(
                        (batch) => batch.id === formData.batch_id
                      );
                      return selectedBatch
                        ? `${selectedBatch.name} (${selectedBatch.code})`
                        : "Select batch (optional)";
                    })()
                  : "Select batch (optional)"}
              </ComboboxTrigger>
              <ComboboxContent>
                <ComboboxCommand>
                  <ComboboxInput placeholder="Search batches..." />
                  <ComboboxList>
                    {loadingBatches ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading batches...
                      </div>
                    ) : batches.length === 0 ? (
                      <ComboboxEmpty>No batches found.</ComboboxEmpty>
                    ) : (
                      <ComboboxGroup>
                        <ComboboxItem
                          value="none"
                          onSelect={() => {
                            handleInputChange("batch_id", "none");
                            setBatchComboboxOpen(false);
                          }}
                        >
                          No batch assignment
                          <ComboboxItemIndicator
                            isSelected={
                              !formData.batch_id || formData.batch_id === "none"
                            }
                          />
                        </ComboboxItem>
                        {batches.map((batch) => {
                          const searchableValue =
                            `${batch.name} ${batch.code} ${batch.status}`.trim();
                          return (
                            <ComboboxItem
                              key={batch.id}
                              value={searchableValue}
                              onSelect={() => {
                                const newValue =
                                  formData.batch_id === batch.id
                                    ? "none"
                                    : batch.id;
                                handleInputChange("batch_id", newValue);
                                setBatchComboboxOpen(false);
                              }}
                            >
                              <div className="flex flex-col">
                                <span className="font-medium">
                                  {batch.name} ({batch.code})
                                </span>
                                <span className="text-xs text-gray-500">
                                  {batch.status} • {batch.weight}kg •{" "}
                                  {batch.cbm_value.toFixed(2)}m³
                                </span>
                              </div>
                              <ComboboxItemIndicator
                                isSelected={formData.batch_id === batch.id}
                              />
                            </ComboboxItem>
                          );
                        })}
                      </ComboboxGroup>
                    )}
                  </ComboboxList>
                </ComboboxCommand>
              </ComboboxContent>
            </Combobox>
            {formData.batch_id && formData.batch_id !== "none" && (
              <p className="text-xs text-blue-600 mt-1">
                ℹ️ This cargo will be assigned to the selected batch for
                consolidated shipping and tracking.
              </p>
            )}
            {batches.length === 0 && !loadingBatches && (
              <p className="text-xs text-gray-500 mt-1">
                No available batches found. Cargo will be created without batch
                assignment.
              </p>
            )}
          </div>
        </div>

        {/* Staff Assignment */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1.5">
              Assign to Staff Member (Optional){" "}
              {loadingStaff && (
                <Loader2 size={12} className="inline animate-spin ml-1" />
              )}
            </label>
            <Combobox
              open={assigneeComboboxOpen}
              onOpenChange={setAssigneeComboboxOpen}
            >
              <ComboboxTrigger
                placeholder="Select staff member (optional)"
                disabled={loadingStaff}
              >
                {formData.assigned_to && formData.assigned_to !== "none"
                  ? (() => {
                      const selectedStaff = staff.find(
                        (member) => member.id === formData.assigned_to
                      );
                      return selectedStaff
                        ? `${selectedStaff.name} (${selectedStaff.accounts?.roles?.name || "No Role"})`
                        : "Select staff member (optional)";
                    })()
                  : "Select staff member (optional)"}
              </ComboboxTrigger>
              <ComboboxContent className="max-h-[200px]">
                <ComboboxCommand>
                  <ComboboxInput placeholder="Search staff..." />
                  <ComboboxList>
                    {loadingStaff ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading staff...
                      </div>
                    ) : staff.length === 0 ? (
                      <ComboboxEmpty>No staff found.</ComboboxEmpty>
                    ) : (
                      <ComboboxGroup>
                        <ComboboxItem
                          value="none"
                          onSelect={() => {
                            handleInputChange("assigned_to", "none");
                            setAssigneeComboboxOpen(false);
                          }}
                        >
                          No staff assignment
                          <ComboboxItemIndicator
                            isSelected={
                              !formData.assigned_to ||
                              formData.assigned_to === "none"
                            }
                          />
                        </ComboboxItem>
                        {staff.map((member) => {
                          const searchableValue =
                            `${member.name} ${member.accounts?.roles?.name || ""}`.trim();
                          return (
                            <ComboboxItem
                              key={member.id}
                              value={searchableValue}
                              onSelect={() => {
                                const newValue =
                                  formData.assigned_to === member.id
                                    ? "none"
                                    : member.id;
                                handleInputChange("assigned_to", newValue);
                                setAssigneeComboboxOpen(false);
                              }}
                            >
                              <div className="flex flex-row justify-start items-center gap-1">
                                <span className="font-medium">
                                  {member.name}
                                </span>
                                <span className="text-xs text-gray-500 m-0">
                                  ({member.accounts?.roles?.name || "No Role"})
                                </span>
                              </div>
                              <ComboboxItemIndicator
                                isSelected={formData.assigned_to === member.id}
                              />
                            </ComboboxItem>
                          );
                        })}
                      </ComboboxGroup>
                    )}
                  </ComboboxList>
                </ComboboxCommand>
              </ComboboxContent>
            </Combobox>
            {formData.assigned_to && formData.assigned_to !== "none" && (
              <p className="text-xs text-green-600 mt-1">
                ✓ This cargo will be assigned to the selected staff member for
                handling.
              </p>
            )}
            {staff.length === 0 && !loadingStaff && (
              <p className="text-xs text-gray-500 mt-1">
                No staff members available for assignment.
              </p>
            )}
          </div>

          {/* Custom Declaration Checkbox */}
          <div>
            <label
              htmlFor=""
              className="block text-sm font-medium text-gray-700 mb-1.5"
            >
              Custom Declaration (optional)
            </label>
            <div className="flex items-center space-x-2 py-[.56em] px-2 bg-gray-50 border border-gray-200 rounded-md">
              <Checkbox
                id="custom_declaration"
                checked={formData.custom_declaration}
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "custom_declaration",
                    checked ? "true" : "false"
                  )
                }
              />
              <small className="text-xs text-primary font-normal leading-none">
                This will mark the cargo for custom declaration.
              </small>
            </div>
          </div>
        </div>

        {/* Freight Details */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-700">
              Freight Details (Optional)
            </h3>
            <span className="text-xs text-gray-500">
              Dimensions help with batch optimization
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-5">
            <InputField
              id="length"
              label="Length (cm)"
              type="number"
              step="0.01"
              placeholder="0"
              value={formData.dimensionLength}
              onChange={(e) =>
                handleInputChange("dimensionLength", e.target.value)
              }
            />
            <InputField
              id="width"
              label="Width (cm)"
              type="number"
              step="0.01"
              placeholder="0"
              value={formData.dimensionWidth}
              onChange={(e) =>
                handleInputChange("dimensionWidth", e.target.value)
              }
            />
            <InputField
              id="height"
              label="Height (cm)"
              type="number"
              step="0.01"
              placeholder="0"
              value={formData.dimensionHeight}
              onChange={(e) =>
                handleInputChange("dimensionHeight", e.target.value)
              }
            />
            <InputField
              id="ctn"
              label="CTN"
              type="number"
              placeholder="0"
              value={formData.CTN}
              onChange={(e) => handleInputChange("CTN", e.target.value)}
            />
            <InputField
              id="cbm"
              label="CBM (calculated)"
              value={calculateCBM().toFixed(4)}
              readOnly={true}
              className="bg-gray-50"
              placeholder="Auto-calculated"
            />
          </div>
        </div>

        {/* Weight Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <SelectField
            id="weightUnit"
            label="Weight Unit"
            placeholder="Select weight unit"
            value={formData.weightUnit}
            options={[
              { value: "KILOGRAMS", label: "Kilograms (kg)" },
              { value: "POUNDS", label: "Pounds (lb)" },
              { value: "GRAMS", label: "Grams (g)" },
              { value: "TONS", label: "Tons (t)" },
              { value: "OUNCES", label: "Ounces (oz)" },
              { value: "SHORT_TON", label: "Short Ton" },
              { value: "LONG_TON", label: "Long Ton" },
            ]}
            onChange={(value) => handleInputChange("weightUnit", value)}
          />
          <InputField
            id="weight"
            label={`Weight (${formData.weightUnit.toLowerCase()})`}
            type="float"
            step="0.01"
            placeholder="0"
            value={formData.weightValue}
            onChange={(e) => handleInputChange("weightValue", e.target.value)}
          />
        </div>

        {/* Pricing Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
          <InputField
            id="quantity"
            label="Quantity"
            type="number"
            placeholder="1"
            value={formData.quantity}
            onChange={(e) => handleInputChange("quantity", e.target.value)}
            min="1"
          />
          <InputField
            id="unit_price"
            label="Unit Price (USD)"
            type="number"
            step="0.01"
            placeholder="0.00"
            value={formData.unit_price}
            onChange={(e) => handleInputChange("unit_price", e.target.value)}
            required
          />
          <InputField
            id="totalPrice"
            label={`Total (USD) / (${factor} × Qty)`}
            type="number"
            step="0.01"
            placeholder="0.00"
            value={formData.totalPrice}
            readOnly={true}
            className="bg-gray-50"
          />
        </div>

        {/* Invoice Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-700">
              Invoice Management
            </h3>
            <span className="text-xs text-gray-500">
              {entityType === "customer"
                ? "Link to existing invoice or create new"
                : "Automatic invoice creation for suppliers"}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1.5">
              {entityType === "customer"
                ? "Select Invoice (Optional)"
                : "Invoice Creation"}{" "}
              {loadingInvoices && (
                <Loader2 size={12} className="inline animate-spin ml-1" />
              )}
            </label>
            <Select
              value={formData.invoice_id}
              onValueChange={(value) => {
                handleInputChange("invoice_id", value);
                if (value) {
                  const selectedInvoice = invoices.find(
                    (inv) => inv.id === value
                  );
                  if (selectedInvoice) {
                    toast.info("Invoice Selected", {
                      description: `New cargo data will be added as a line item to Invoice ${selectedInvoice.inv_number}, affecting the total invoice amount.`,
                    });
                  }
                }
              }}
              disabled={
                (entityType === "customer" && !formData.customer_id) ||
                (entityType === "supplier" && !formData.supplier_id) ||
                loadingInvoices
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue
                  placeholder={
                    entityType === "customer" && !formData.customer_id
                      ? "Select customer first"
                      : entityType === "supplier" && !formData.supplier_id
                        ? "Select supplier first"
                        : invoices.length === 0
                          ? "No invoices found - will create new"
                          : "Select existing invoice or leave empty for new"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {invoices.map((invoice) => (
                  <SelectItem key={invoice.id} value={invoice.id}>
                    {invoice.inv_number} - $
                    {invoice.total?.toFixed(2) || "0.00"} ({invoice.status})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {entityType === "customer" &&
              formData.customer_id &&
              invoices.length === 0 &&
              !loadingInvoices && (
                <p className="text-xs text-gray-500 mt-1">
                  No existing invoices found for this customer. A new invoice
                  will be created automatically.
                </p>
              )}
            {entityType === "customer" && formData.invoice_id && (
              <p className="text-xs text-amber-600 mt-1">
                ⚠️ This cargo will be added as a line item to the selected
                invoice, updating the total amount.
              </p>
            )}
            {entityType === "supplier" && formData.supplier_id && (
              <p className="text-xs text-blue-600 mt-1">
                ℹ️ A new invoice will be created automatically for this supplier
                cargo. Supplier invoices are used for tracking and
                record-keeping purposes.
              </p>
            )}
          </div>
        </div>

        {/* Add Document Uploader Component */}

        {/* Most recent client invoice banner */}
        {entityType === "customer" && formData.customer_id && (
          <div className="space-y-3">
            {loadingRecentInvoice ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <Loader2 size={16} className="animate-spin text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Loading recent invoice information...
                  </span>
                </div>
              </div>
            ) : recentPaidInvoice ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-green-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-green-800">
                        Most Recent Paid Invoice
                      </h4>
                      <div className="mt-1 text-sm text-green-700">
                        <span className="font-medium">
                          {recentPaidInvoice.inv_number ||
                            `Invoice ${recentPaidInvoice.id.slice(0, 8)}`}
                        </span>
                        {" • "}
                        <span className="font-semibold">
                          ${(recentPaidInvoice.total || 0).toLocaleString()}
                        </span>
                        {" • "}
                        <span>
                          {new Date(
                            recentPaidInvoice.created_at
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      PAID
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-700">
                      No Recent Paid Invoices
                    </h4>
                    <p className="mt-1 text-sm text-gray-500">
                      This customer has no paid invoices on record.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Document Upload Section */}
        <div className="space-y-4">
          <Label className="text-sm font-medium text-gray-700 mb-2">
            <FileText className="h-4 w-4 inline mr-1" />
            Attach Documents (Optional)
          </Label>

          {/* Current Attached Files */}
          {attachedFiles.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 mb-2">
                Attached Files ({attachedFiles.length})
              </Label>
              <div className="space-y-2">
                {attachedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border"
                  >
                    <div className="flex items-center gap-2">
                      {getFileTypeIcon(file.name)}
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAttachedFile(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* File Upload Area */}
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors"
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              type="file"
              id="document-upload"
              className="hidden"
              multiple
              accept=".pdf,.doc,.docx,.xlsx,.xls,.png,.jpg,.jpeg"
              onChange={handleFileSelect}
              disabled={isSubmitting}
            />
            <div>
              <Upload className="h-8 w-8 mx-auto text-gray-400" />
              <div>
                <button
                  type="button"
                  onClick={() =>
                    document.getElementById("document-upload")?.click()
                  }
                  className="text-primary hover:text-primary/80 font-medium"
                  disabled={isSubmitting}
                >
                  Click to upload
                </button>
                <span className="text-gray-500"> or drag and drop</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                PDF, DOC, XLSX, Images (Max 10MB each)
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="mt-4 pt-4 border-t border-gray-200">
          <DialogClose asChild>
            <Button type="button" variant="outline" disabled={isSubmitting}>
              Cancel
            </Button>
          </DialogClose>
          <Button
            type="button"
            variant="outline"
            disabled={isSubmitting || !formData.type}
            onClick={handleAddAnother}
          >
            {isSubmitting ? (
              <Loader2 size={16} className="animate-spin mr-2" />
            ) : (
              <Plus className="h-4 w-4 mr-2" />
            )}
            Add Another
          </Button>
          <Button type="submit" disabled={isSubmitting || !formData.type}>
            {isSubmitting ? (
              <Loader2 size={16} className="animate-spin mr-2" />
            ) : (
              <Plus className="h-4 w-4 mr-2" />
            )}
            {isSubmitting
              ? "Creating..."
              : `Create & Close${attachedFiles.length > 0 ? ` & Upload ${attachedFiles.length} File${attachedFiles.length > 1 ? "s" : ""}` : ""}`}
          </Button>
        </DialogFooter>
      </form>
    );
  }
);

NewCargoForm.displayName = "NewCargoForm";
