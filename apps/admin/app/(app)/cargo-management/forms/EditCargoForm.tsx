"use client";

import React, { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@workspace/ui/components/combobox";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { AnimatedTabGroup } from "@/components/animated-tab-group";
import { useAppSelector } from "@/store/hooks";
import { toast } from "sonner";
import { cn } from "@workspace/ui/lib/utils";
import { trimming } from "@/lib/utils";
import {
  cargoService,
  customerService,
  supplierService,
  invoiceService,
  batchService,
  logService,
  userService,
  type CargoWithRelations,
  type Customer,
  type Supplier,
  type InvoiceWithCustomer,
  type BatchWithRelations,
  type UserWithAccount,
  Cargo_Categories,
  CARGO_CATEGORY_LABELS,
} from "@/lib/logistics";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@workspace/ui/components/dialog";

// Input Field Component
const InputField = ({
  id,
  label,
  icon: Icon,
  ...props
}: {
  id: string;
  label: string;
  icon?: React.ElementType;
  [key: string]: any;
} & React.InputHTMLAttributes<HTMLInputElement>) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <div className="relative">
      {Icon && (
        <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
      )}
      <Input
        id={id}
        className={cn(
          "w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary",
          Icon ? "pl-10" : ""
        )}
        {...props}
      />
    </div>
  </div>
);

// Select Field Component
const SelectField = ({
  id,
  label,
  placeholder,
  defaultValue,
  value,
  options,
  onChange,
  ...props
}: {
  id: string;
  label: string;
  placeholder?: string;
  defaultValue?: string;
  value?: string;
  options: (string | { value: string; label: string })[];
  onChange?: (value: string) => void;
  [key: string]: any;
}) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <Select
      defaultValue={defaultValue}
      value={value}
      onValueChange={onChange}
      {...props}
    >
      <SelectTrigger
        id={id}
        className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10"
      >
        <SelectValue placeholder={placeholder ?? "Select..."} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => {
          const value =
            typeof option === "string"
              ? option === "All Types" || option === "All Statuses"
                ? "all"
                : option.toLowerCase().replace(/\s+/g, "-")
              : option.value;
          const displayLabel =
            typeof option === "string" ? option : option.label;
          return (
            <SelectItem key={value} value={value} className="text-sm text-wrap">
              {trimming(displayLabel, 20)}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  </div>
);

// EditCargoForm Props Interface
interface EditCargoFormProps {
  cargo: CargoWithRelations | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export const EditCargoForm: React.FC<EditCargoFormProps> = ({
  cargo,
  onSuccess,
  onCancel,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [invoices, setInvoices] = useState<InvoiceWithCustomer[]>([]);
  const [batches, setBatches] = useState<BatchWithRelations[]>([]);
  const [staff, setStaff] = useState<UserWithAccount[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingSuppliers, setLoadingSuppliers] = useState(true);
  const [loadingInvoices, setLoadingInvoices] = useState(false);
  const [loadingBatches, setLoadingBatches] = useState(true);
  const [loadingStaff, setLoadingStaff] = useState(true);
  const [factor, setFactor] = useState("Weight");
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingSubmitData, setPendingSubmitData] = useState<any>(null);
  const [entityType, setEntityType] = useState<"customer" | "supplier">(
    cargo?.customers
      ? "customer"
      : (cargo as any)?.suppliers
        ? "supplier"
        : "customer"
  );

  // Combobox states
  const [customerComboboxOpen, setCustomerComboboxOpen] = useState(false);
  const [supplierComboboxOpen, setSupplierComboboxOpen] = useState(false);
  const [assigneeComboboxOpen, setAssigneeComboboxOpen] = useState(false);
  const [batchComboboxOpen, setBatchComboboxOpen] = useState(false);
  const [formData, setFormData] = useState({
    type: (cargo as any)?.type || (cargo as any)?.category || "",
    customer_id: cargo?.customer_id || "",
    supplier_id: (cargo as any)?.supplier_id || "",
    particular: cargo?.particular || "",
    china_tracking_number: (cargo as any)?.china_tracking_number || "",
    factor_unit: cargo?.factor_unit,
    quantity: cargo?.quantity?.toString() || "1",
    dimensionLength: cargo?.dimension_length?.toString() || "",
    dimensionWidth: cargo?.dimension_width?.toString() || "",
    dimensionHeight: cargo?.dimension_height?.toString() || "",
    weightValue: cargo?.weight_value?.toString() || "",
    weightUnit: (cargo as any)?.weight_unit || "KILOGRAMS",
    custom_declaration: (cargo as any)?.custom_declaration || false,
    ctn: (cargo as any)?.ctn?.toString() || "",
    totalPrice: (cargo as any)?.total_price?.toString() || "",
    unit_price: (cargo as any)?.unit_price?.toString() || "",
    invoice_id: (cargo as any)?.invoice_id || "",
    batch_id: cargo?.batch_id || "",
    assigned_to: cargo?.assigned_to || "", // Staff member assigned to handle this cargo
  });

  // Get authenticated user
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Reset form data when cargo changes
  useEffect(() => {
    if (cargo) {
      setFormData({
        type: (cargo as any)?.type || (cargo as any)?.category || "",
        customer_id: cargo?.customer_id || "",
        supplier_id: (cargo as any)?.supplier_id || "",
        particular: cargo?.particular || "",
        china_tracking_number: (cargo as any)?.china_tracking_number || "",
        factor_unit: cargo?.factor_unit,
        quantity: cargo?.quantity?.toString() || "1",
        dimensionLength: cargo?.dimension_length?.toString() || "",
        dimensionWidth: cargo?.dimension_width?.toString() || "",
        dimensionHeight: cargo?.dimension_height?.toString() || "",
        weightValue: cargo?.weight_value?.toString() || "",
        weightUnit: (cargo as any)?.weight_unit || "KILOGRAMS",
        custom_declaration: (cargo as any)?.custom_declaration || false,
        ctn: (cargo as any)?.ctn?.toString() || "",
        totalPrice: (cargo as any)?.total_price?.toString() || "",
        unit_price: (cargo as any)?.unit_price?.toString() || "",
        invoice_id: (cargo as any)?.invoice_id || "",
        batch_id: cargo?.batch_id || "",
        assigned_to: cargo?.assigned_to || "", // Staff member assigned to handle this cargo
      });

      // Set entity type based on cargo data
      setEntityType(
        cargo?.customers
          ? "customer"
          : (cargo as any)?.suppliers
            ? "supplier"
            : "customer"
      );
    }
  }, [cargo]);

  // Fetch customers, suppliers, and batches for dropdown
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch customers
        const customersResult = await customerService.getActiveCustomers();
        if (customersResult.success) {
          setCustomers(customersResult.data);
        }
        setLoadingCustomers(false);

        // Fetch suppliers
        const suppliersResult = await supplierService.getActiveSuppliers();
        if (suppliersResult.success) {
          setSuppliers(suppliersResult.data);
        }
        setLoadingSuppliers(false);

        // Fetch available batches
        const batchesResult = await batchService.getAvailableBatches(undefined);
        if (batchesResult.success) {
          setBatches(batchesResult.data);
        }
        setLoadingBatches(false);

        // Fetch staff members for assignment
        const staffResult = await userService.getUsersForAssignment([]);
        if (staffResult.success) {
          setStaff(staffResult.data);
        }
        setLoadingStaff(false);
      } catch (error) {
        console.error("Error fetching form data:", error);
        setLoadingCustomers(false);
        setLoadingSuppliers(false);
        setLoadingBatches(false);
        setLoadingStaff(false);
      }
    };

    fetchData();
  }, []);

  // Fetch invoices when customer or supplier is selected
  useEffect(() => {
    const fetchInvoices = async () => {
      // Only fetch invoices for customers, not suppliers
      if (entityType !== "customer" || !formData.customer_id) {
        setInvoices([]);
        return;
      }

      try {
        setLoadingInvoices(true);
        const invoicesResult = await invoiceService.getAllInvoicesWithCustomers(
          {
            limit: 50,
          }
        );

        if (invoicesResult.success && invoicesResult.data) {
          // Filter invoices by selected customer
          const customerInvoices = invoicesResult.data.filter(
            (invoice) => invoice.customer_id === formData.customer_id
          );
          setInvoices(customerInvoices);
        }
      } catch (error) {
        console.error("Error fetching invoices:", error);
      } finally {
        setLoadingInvoices(false);
      }
    };

    fetchInvoices();
  }, [formData.customer_id, entityType]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleEntityTypeChange = (type: "customer" | "supplier") => {
    setEntityType(type);
    // Clear the opposite entity selection when switching
    if (type === "customer") {
      setFormData((prev) => ({ ...prev, supplier_id: "" }));
    } else {
      setFormData((prev) => ({ ...prev, customer_id: "", invoice_id: "" }));
      setInvoices([]); // Clear invoices when switching to supplier
    }
  };

  // Calculate CBM automatically (in cubic meters, converted from cm)
  const calculateCBM = () => {
    const length = parseFloat(formData.dimensionLength) || 0;
    const width = parseFloat(formData.dimensionWidth) || 0;
    const height = parseFloat(formData.dimensionHeight) || 0;
    const ctn = parseInt(formData.ctn) || 0;

    let dimensions = length * width * height * ctn;
    let meter_cube = dimensions / 1000000;

    // Convert from cm³ to m³ by dividing by 1,000,000
    return meter_cube;
  };

  // Calculate total price automatically based on factor (weight or CBM)
  const calculateTotalPrice = () => {
    const { unit_price, weightValue, quantity } = formData;

    const CBM = calculateCBM();

    const weight = parseFloat(weightValue) || 0;
    const unitPriceNum = parseFloat(unit_price) || 0;
    const qty = parseInt(quantity) || 1;

    // Determine factor: use weight if available, otherwise use CBM
    let factorValue = weight || CBM;

    // Update factor state to track what's being used for calculation
    if (CBM) {
      setFactor("CBM");
      factorValue = CBM;
    } else if (weight) {
      setFactor("Weight");
      factorValue = weight;
    }

    return unitPriceNum * factorValue * qty;
  };

  // Auto-update total price when relevant fields change
  useEffect(() => {
    const newTotalPrice = calculateTotalPrice();
    setFormData((prev) => ({
      ...prev,
      totalPrice: newTotalPrice.toString(),
    }));
  }, [
    formData.unit_price,
    formData.ctn,
    formData.weightValue,
    formData.dimensionLength,
    formData.dimensionWidth,
    formData.dimensionHeight,
    formData.quantity,
  ]);

  let cbm_value: number = calculateCBM();
  let weightValue = parseFloat(formData.weightValue);
  let weightUnit = formData.weightUnit;

  if (!weightValue) {
    weightUnit = "KILOGRAMS";
    weightValue = cbm_value * 167;
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!authUser || !cargo) return;

    // Validate that either customer or supplier is selected
    if (entityType === "customer" && !formData.customer_id) {
      toast.error("Validation Error", {
        description: "Please select a customer.",
      });
      return;
    }
    if (entityType === "supplier" && !formData.supplier_id) {
      toast.error("Validation Error", {
        description: "Please select a supplier.",
      });
      return;
    }

    // Prepare update data
    const updateData = {
      type: formData.type,
      customer_id: entityType === "customer" ? formData.customer_id : null,
      supplier_id: entityType === "supplier" ? formData.supplier_id : null,
      particular: formData.particular,
      china_tracking_number: formData.china_tracking_number || null,
      quantity: parseInt(formData.quantity) || 0,
      dimension_length: parseFloat(formData.dimensionLength) || 0,
      dimension_width: parseFloat(formData.dimensionWidth) || 0,
      dimension_height: parseFloat(formData.dimensionHeight) || 0,
      dimension_unit: "METERS" as const,
      weight_value: weightValue,
      weight_unit: weightUnit as any,
      ctn: parseInt(formData.ctn) || null,
      cbm_value: cbm_value,
      cbm_unit: "METER_CUBIC" as const,
      unit_price: parseFloat(formData.unit_price) || 0,
      total_price: parseFloat(formData.totalPrice) || 0,
      invoice_id: formData.invoice_id || null,
      batch_id: formData.batch_id || null,
      assigned_to:
        formData.assigned_to === "none" ? null : formData.assigned_to || null, // Staff member assignment
    };

    // Check if there are significant changes that need confirmation
    const hasInvoiceChange = updateData.invoice_id !== cargo.invoice_id;
    const hasBatchChange = updateData.batch_id !== cargo.batch_id;

    if (hasInvoiceChange || hasBatchChange) {
      // Store the update data and show confirmation dialog
      setPendingSubmitData(updateData);
      setShowConfirmDialog(true);
    } else {
      // No significant changes, proceed directly
      await performUpdate(updateData);
    }
  };

  const performUpdate = async (updateData: any) => {
    setIsSubmitting(true);
    try {
      // Update cargo with invoice updates
      const result = await cargoService.updateCargoWithInvoiceUpdates(
        cargo!.id,
        updateData
      );

      if (result.success) {
        // Log the change to tracking history
        await logTrackingHistory(updateData);

        toast.success("Cargo Updated Successfully", {
          description:
            "Cargo details and associated invoices have been updated.",
        });
        onSuccess();
      } else {
        toast.error("Update Failed", {
          description: result.error || "Failed to update cargo",
        });
      }
    } catch (error: any) {
      console.error("Error updating cargo:", error);
      toast.error("Update Failed", {
        description: error.message || "An unexpected error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const logTrackingHistory = async (updateData: any) => {
    if (!authUser || !cargo) return;

    try {
      const changes = [];

      // Check for batch assignment change
      if (updateData.batch_id !== cargo.batch_id) {
        const oldBatch = cargo.batch_id
          ? batches.find((b) => b.id === cargo.batch_id)?.name ||
            "Unknown Batch"
          : "No Batch";
        const newBatch = updateData.batch_id
          ? batches.find((b) => b.id === updateData.batch_id)?.name ||
            "Unknown Batch"
          : "No Batch";
        changes.push(
          `Batch assignment changed from "${oldBatch}" to "${newBatch}"`
        );
      }

      // Check for invoice change
      if (updateData.invoice_id !== cargo.invoice_id) {
        const oldInvoice = cargo.invoice_id
          ? `Invoice ${cargo.invoice_id}`
          : "No Invoice";
        const newInvoice = updateData.invoice_id
          ? `Invoice ${updateData.invoice_id}`
          : "No Invoice";
        changes.push(
          `Invoice assignment changed from "${oldInvoice}" to "${newInvoice}"`
        );
      }

      // Check for assigned user change
      if (updateData.assigned_to !== cargo.assigned_to) {
        const oldAssignee = cargo.assigned_to
          ? staff.find((s) => s.id === cargo.assigned_to)?.name ||
            "Unknown User"
          : "Unassigned";
        const newAssignee = updateData.assigned_to
          ? staff.find((s) => s.id === updateData.assigned_to)?.name ||
            "Unknown User"
          : "Unassigned";
        changes.push(
          `Staff assignment changed from "${oldAssignee}" to "${newAssignee}"`
        );
      }

      if (changes.length > 0) {
        await logService.logUserAction(
          authUser.accountId,
          "CARGO_UPDATE",
          `Cargo ${cargo.tracking_number || cargo.id} updated: ${changes.join(", ")}`,
          "cargos",
          cargo.id
        );
      }
    } catch (error) {
      console.error("Error logging tracking history:", error);
      // Don't fail the update if logging fails
    }
  };

  const handleConfirmUpdate = async () => {
    setShowConfirmDialog(false);
    if (pendingSubmitData) {
      await performUpdate(pendingSubmitData);
      setPendingSubmitData(null);
    }
  };

  const handleCancelUpdate = () => {
    setShowConfirmDialog(false);
    setPendingSubmitData(null);
  };

  return (
    <form onSubmit={handleSubmit} className="w-4xl grid gap-5 py-4">
      {/* Tab Selector for Customer/Supplier */}
      <div className="mb-3">
        <AnimatedTabGroup
          tabs={[
            {
              id: "customer",
              label: "Customer",
              content: (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">
                    Customer{" "}
                    {loadingCustomers && (
                      <Loader2 size={12} className="inline animate-spin ml-1" />
                    )}
                  </label>
                  <Combobox
                    open={customerComboboxOpen}
                    onOpenChange={setCustomerComboboxOpen}
                  >
                    <ComboboxTrigger
                      placeholder="Select Customer"
                      disabled={loadingCustomers}
                    >
                      {formData.customer_id
                        ? (() => {
                            const selectedCustomer = customers.find(
                              (customer) => customer.id === formData.customer_id
                            );
                            return selectedCustomer
                              ? `${selectedCustomer.name}${
                                  selectedCustomer.email
                                    ? ` (${selectedCustomer.email})`
                                    : ""
                                }`
                              : "Select Customer";
                          })()
                        : "Select Customer"}
                    </ComboboxTrigger>
                    <ComboboxContent>
                      <ComboboxCommand>
                        <ComboboxInput placeholder="Search customers..." />
                        <ComboboxList>
                          {loadingCustomers ? (
                            <div className="flex items-center justify-center p-2">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              Loading customers...
                            </div>
                          ) : customers.length === 0 ? (
                            <ComboboxEmpty>No customers found.</ComboboxEmpty>
                          ) : (
                            <ComboboxGroup>
                              {customers.map((customer) => {
                                const searchableValue =
                                  `${customer.name} ${customer.email || ""}`.trim();
                                return (
                                  <ComboboxItem
                                    key={customer.id}
                                    value={searchableValue}
                                    onSelect={() => {
                                      const newValue =
                                        formData.customer_id === customer.id
                                          ? ""
                                          : customer.id;
                                      handleInputChange(
                                        "customer_id",
                                        newValue
                                      );
                                      setCustomerComboboxOpen(false);
                                    }}
                                  >
                                    {customer.name}{" "}
                                    {customer.email && `(${customer.email})`}
                                    <ComboboxItemIndicator
                                      isSelected={
                                        formData.customer_id === customer.id
                                      }
                                    />
                                  </ComboboxItem>
                                );
                              })}
                            </ComboboxGroup>
                          )}
                        </ComboboxList>
                      </ComboboxCommand>
                    </ComboboxContent>
                  </Combobox>
                </div>
              ),
            },
            {
              id: "supplier",
              label: "Supplier",
              content: (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">
                    Supplier{" "}
                    {loadingSuppliers && (
                      <Loader2 size={12} className="inline animate-spin ml-1" />
                    )}
                  </label>
                  <Combobox
                    open={supplierComboboxOpen}
                    onOpenChange={setSupplierComboboxOpen}
                  >
                    <ComboboxTrigger
                      placeholder="Select Supplier"
                      disabled={loadingSuppliers}
                    >
                      {formData.supplier_id
                        ? (() => {
                            const selectedSupplier = suppliers.find(
                              (supplier) => supplier.id === formData.supplier_id
                            );
                            if (selectedSupplier) {
                              const displayName =
                                selectedSupplier.tracking_number ||
                                selectedSupplier.phone ||
                                selectedSupplier.location ||
                                `Supplier ${selectedSupplier.id}`;
                              return `${displayName}${
                                selectedSupplier.phone
                                  ? ` (${selectedSupplier.phone})`
                                  : ""
                              }`;
                            }
                            return "Select Supplier";
                          })()
                        : "Select Supplier"}
                    </ComboboxTrigger>
                    <ComboboxContent>
                      <ComboboxCommand>
                        <ComboboxInput placeholder="Search suppliers..." />
                        <ComboboxList>
                          {loadingSuppliers ? (
                            <div className="flex items-center justify-center p-2">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              Loading suppliers...
                            </div>
                          ) : suppliers.length === 0 ? (
                            <ComboboxEmpty>No suppliers found.</ComboboxEmpty>
                          ) : (
                            <ComboboxGroup>
                              {suppliers.map((supplier) => {
                                const displayName =
                                  supplier.tracking_number ||
                                  supplier.phone ||
                                  supplier.location ||
                                  `Supplier ${supplier.id}`;
                                const searchableValue =
                                  `${displayName} ${supplier.phone || ""} ${supplier.location || ""}`.trim();
                                return (
                                  <ComboboxItem
                                    key={supplier.id}
                                    value={searchableValue}
                                    onSelect={() => {
                                      const newValue =
                                        formData.supplier_id === supplier.id
                                          ? ""
                                          : supplier.id;
                                      handleInputChange(
                                        "supplier_id",
                                        newValue
                                      );
                                      setSupplierComboboxOpen(false);
                                    }}
                                  >
                                    {displayName}
                                    {supplier.phone && ` (${supplier.phone})`}
                                    <ComboboxItemIndicator
                                      isSelected={
                                        formData.supplier_id === supplier.id
                                      }
                                    />
                                  </ComboboxItem>
                                );
                              })}
                            </ComboboxGroup>
                          )}
                        </ComboboxList>
                      </ComboboxCommand>
                    </ComboboxContent>
                  </Combobox>
                </div>
              ),
            },
          ]}
          defaultTabId={entityType}
          onTabChange={(tabId: string) =>
            handleEntityTypeChange(tabId as "customer" | "supplier")
          }
          className="border border-gray-200 rounded-lg bg-gray-50 p-2"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <InputField
          id="description"
          label="Cargo Description"
          placeholder="e.g: Electronics, 10 pallets"
          value={formData.particular}
          onChange={(e) => handleInputChange("particular", e.target.value)}
        />
        {/* Cargo Type */}
        <div>
          <SelectField
            id="cargoType"
            label="Cargo Type"
            placeholder="Select Cargo Type"
            value={formData.type}
            options={[
              {
                value: Cargo_Categories.DANGEROUS,
                label: CARGO_CATEGORY_LABELS[Cargo_Categories.DANGEROUS],
              },
              {
                value: Cargo_Categories.SAFE,
                label: CARGO_CATEGORY_LABELS[Cargo_Categories.SAFE],
              },
            ]}
            onChange={(value) => handleInputChange("type", value)}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <InputField
          id="china_tracking_number"
          label="China Tracking Number"
          placeholder="e.g: Previous tracking reference (optional)"
          value={formData.china_tracking_number}
          onChange={(e) =>
            handleInputChange("china_tracking_number", e.target.value)
          }
        />

        {/* Batch Selector */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1.5">
            Batch Assignment (Optional){" "}
            {loadingBatches && (
              <Loader2 size={12} className="inline animate-spin ml-1" />
            )}
          </label>
          <Combobox
            open={batchComboboxOpen}
            onOpenChange={setBatchComboboxOpen}
          >
            <ComboboxTrigger
              placeholder="Select batch (optional)"
              disabled={loadingBatches}
            >
              {formData.batch_id && formData.batch_id !== "none"
                ? (() => {
                    const selectedBatch = batches.find(
                      (batch) => batch.id === formData.batch_id
                    );
                    return selectedBatch
                      ? `${selectedBatch.name} (${selectedBatch.code})`
                      : "Select batch (optional)";
                  })()
                : "Select batch (optional)"}
            </ComboboxTrigger>
            <ComboboxContent>
              <ComboboxCommand>
                <ComboboxInput placeholder="Search batches..." />
                <ComboboxList>
                  {loadingBatches ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading batches...
                    </div>
                  ) : batches.length === 0 ? (
                    <ComboboxEmpty>No batches found.</ComboboxEmpty>
                  ) : (
                    <ComboboxGroup>
                      <ComboboxItem
                        value="none"
                        onSelect={() => {
                          handleInputChange("batch_id", "none");
                          setBatchComboboxOpen(false);
                        }}
                      >
                        No batch assignment
                        <ComboboxItemIndicator
                          isSelected={
                            !formData.batch_id || formData.batch_id === "none"
                          }
                        />
                      </ComboboxItem>
                      {batches.map((batch) => {
                        const searchableValue =
                          `${batch.name} ${batch.code} ${batch.status}`.trim();
                        return (
                          <ComboboxItem
                            key={batch.id}
                            value={searchableValue}
                            onSelect={() => {
                              const newValue =
                                formData.batch_id === batch.id
                                  ? "none"
                                  : batch.id;
                              handleInputChange("batch_id", newValue);
                              setBatchComboboxOpen(false);
                            }}
                          >
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {batch.name} ({batch.code})
                              </span>
                              <span className="text-xs text-gray-500">
                                {batch.status} • {batch.weight}kg •{" "}
                                {batch.cbm_value.toFixed(2)}m³
                              </span>
                            </div>
                            <ComboboxItemIndicator
                              isSelected={formData.batch_id === batch.id}
                            />
                          </ComboboxItem>
                        );
                      })}
                    </ComboboxGroup>
                  )}
                </ComboboxList>
              </ComboboxCommand>
            </ComboboxContent>
          </Combobox>
          {formData.batch_id && (
            <p className="text-xs text-blue-600 mt-1">
              ℹ️ This cargo will be assigned to the selected batch for
              consolidated shipping and tracking.
            </p>
          )}
          {batches.length === 0 && !loadingBatches && (
            <p className="text-xs text-gray-500 mt-1">
              No available batches found. Cargo will be updated without batch
              assignment.
            </p>
          )}
        </div>

        {/* Staff Assignment */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1.5">
            Assign to Staff Member (Optional){" "}
            {loadingStaff && (
              <Loader2 size={12} className="inline animate-spin ml-1" />
            )}
          </label>
          <Combobox
            open={assigneeComboboxOpen}
            onOpenChange={setAssigneeComboboxOpen}
          >
            <ComboboxTrigger
              placeholder="Select staff member (optional)"
              disabled={loadingStaff}
            >
              {formData.assigned_to && formData.assigned_to !== "none"
                ? (() => {
                    const selectedStaff = staff.find(
                      (member) => member.id === formData.assigned_to
                    );
                    return selectedStaff
                      ? `${selectedStaff.name} (${selectedStaff.accounts?.roles?.name || "No Role"})`
                      : "Select staff member (optional)";
                  })()
                : "Select staff member (optional)"}
            </ComboboxTrigger>
            <ComboboxContent className="max-h-[200px]">
              <ComboboxCommand>
                <ComboboxInput placeholder="Search staff..." />
                <ComboboxList>
                  {loadingStaff ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading staff...
                    </div>
                  ) : staff.length === 0 ? (
                    <ComboboxEmpty>No staff found.</ComboboxEmpty>
                  ) : (
                    <ComboboxGroup>
                      <ComboboxItem
                        value="none"
                        onSelect={() => {
                          setFormData((prev) => ({
                            ...prev,
                            assigned_to: "none",
                          }));
                          setAssigneeComboboxOpen(false);
                        }}
                      >
                        No staff assignment
                        <ComboboxItemIndicator
                          isSelected={
                            !formData.assigned_to ||
                            formData.assigned_to === "none"
                          }
                        />
                      </ComboboxItem>
                      {staff.map((member) => {
                        const searchableValue =
                          `${member.name} ${member.accounts?.roles?.name || ""}`.trim();
                        return (
                          <ComboboxItem
                            key={member.id}
                            value={searchableValue}
                            onSelect={() => {
                              const newValue =
                                formData.assigned_to === member.id
                                  ? "none"
                                  : member.id;
                              setFormData((prev) => ({
                                ...prev,
                                assigned_to: newValue,
                              }));
                              setAssigneeComboboxOpen(false);
                            }}
                          >
                            <div className="flex flex-row justify-start items-center gap-1">
                              <span className="font-medium">{member.name}</span>
                              <span className="text-xs text-gray-500 m-0">
                                ({member.accounts?.roles?.name || "No Role"})
                              </span>
                            </div>
                            <ComboboxItemIndicator
                              isSelected={formData.assigned_to === member.id}
                            />
                          </ComboboxItem>
                        );
                      })}
                    </ComboboxGroup>
                  )}
                </ComboboxList>
              </ComboboxCommand>
            </ComboboxContent>
          </Combobox>
          {formData.assigned_to && formData.assigned_to !== "none" && (
            <p className="text-xs text-green-600 mt-1">
              ✓ This cargo will be assigned to the selected staff member for
              handling.
            </p>
          )}
          {staff.length === 0 && !loadingStaff && (
            <p className="text-xs text-gray-500 mt-1">
              No staff members available for assignment.
            </p>
          )}
        </div>

        {/* Custom Declaration Checkbox */}
        <div>
          <label
            htmlFor=""
            className="block text-sm font-medium text-gray-700 mb-1.5"
          >
            Custom Declaration (optional)
          </label>
          <div className="flex items-center space-x-2 py-[.56em] px-2 bg-gray-50 border border-gray-200 rounded-md">
            <Checkbox
              id="custom_declaration"
              checked={formData.custom_declaration}
              onCheckedChange={(checked) =>
                handleInputChange(
                  "custom_declaration",
                  checked ? "true" : "false"
                )
              }
            />
            <small className="text-xs text-primary font-normal leading-none">
              This will mark the cargo for custom declaration.
            </small>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-700">
            Freight Details (Optional)
          </h3>
          <span className="text-xs text-gray-500">
            Dimensions help with batch optimization
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-5">
          <InputField
            id="length"
            label="Length (cm)"
            type="float"
            placeholder="0"
            value={formData.dimensionLength}
            onChange={(e) =>
              handleInputChange("dimensionLength", e.target.value)
            }
          />
          <InputField
            id="width"
            label="Width (cm)"
            type="float"
            placeholder="0"
            value={formData.dimensionWidth}
            onChange={(e) =>
              handleInputChange("dimensionWidth", e.target.value)
            }
          />
          <InputField
            id="height"
            label="Height (cm)"
            type="float"
            placeholder="0"
            value={formData.dimensionHeight}
            onChange={(e) =>
              handleInputChange("dimensionHeight", e.target.value)
            }
          />
          <InputField
            id="ctn"
            label="CTN"
            type="number"
            placeholder="0"
            value={formData.ctn}
            onChange={(e) => handleInputChange("ctn", e.target.value)}
          />
          <InputField
            id="cbm"
            label="CBM (calculated)"
            value={calculateCBM().toFixed(4)}
            readOnly={true}
            className="bg-gray-50"
            placeholder="Auto-calculated"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <SelectField
          id="weightUnit"
          label="Weight Unit"
          placeholder="Select weight unit"
          value={formData.weightUnit}
          options={[
            { value: "KILOGRAMS", label: "Kilograms (kg)" },
            { value: "POUNDS", label: "Pounds (lb)" },
            { value: "GRAMS", label: "Grams (g)" },
            { value: "TONS", label: "Tons (t)" },
            { value: "OUNCES", label: "Ounces (oz)" },
            { value: "SHORT_TON", label: "Short Ton" },
            { value: "LONG_TON", label: "Long Ton" },
          ]}
          onChange={(value) => handleInputChange("weightUnit", value)}
        />
        <InputField
          id="weight"
          label={`Weight (${formData.weightUnit.toLowerCase()})`}
          type="float"
          placeholder="0"
          value={formData.weightValue}
          onChange={(e) => handleInputChange("weightValue", e.target.value)}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        <InputField
          id="quantity"
          label="Quantity"
          type="number"
          placeholder="1"
          value={formData.quantity}
          onChange={(e) => handleInputChange("quantity", e.target.value)}
          min="1"
        />
        <InputField
          id="unit_price"
          label="Unit Price (USD)"
          type="float"
          placeholder="0.00"
          value={formData.unit_price}
          onChange={(e) => handleInputChange("unit_price", e.target.value)}
          required
        />
        <InputField
          id="totalPrice"
          label={`Total (USD) / (${factor} × Qty)`}
          type="float"
          placeholder="0.00"
          value={formData.totalPrice}
          readOnly={true}
        />
      </div>

      {/* Invoice Section - Handles both Customers and Suppliers */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-700">
            Invoice Management
          </h3>
          <span className="text-xs text-gray-500">
            {entityType === "customer"
              ? "Link to existing invoice or create new"
              : "Automatic invoice creation for suppliers"}
          </span>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1.5">
            {entityType === "customer"
              ? "Select Invoice (Optional)"
              : "Invoice Creation"}{" "}
            {loadingInvoices && (
              <Loader2 size={12} className="inline animate-spin ml-1" />
            )}
          </label>
          <Select
            value={formData.invoice_id}
            onValueChange={(value) => {
              handleInputChange("invoice_id", value);
              // Show toast notification when existing invoice is selected
              if (value) {
                const selectedInvoice = invoices.find(
                  (inv) => inv.id === value
                );
                if (selectedInvoice) {
                  toast.info("Invoice Selected", {
                    description: `Cargo will be moved to Invoice ${selectedInvoice.inv_number}. This will update the invoice totals and line items.`,
                  });
                }
              }
            }}
            disabled={
              (entityType === "customer" && !formData.customer_id) ||
              (entityType === "supplier" && !formData.supplier_id) ||
              loadingInvoices
            }
          >
            <SelectTrigger className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10">
              <SelectValue
                placeholder={
                  entityType === "customer" && !formData.customer_id
                    ? "Select customer first"
                    : entityType === "supplier" && !formData.supplier_id
                      ? "Select supplier first"
                      : invoices.length === 0
                        ? "No invoices found - will create new"
                        : "Select existing invoice or leave empty for new"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {invoices.map((invoice) => (
                <SelectItem key={invoice.id} value={invoice.id}>
                  {invoice.inv_number} - ${invoice.total?.toFixed(2) || "0.00"}{" "}
                  ({invoice.status})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {entityType === "customer" &&
            formData.customer_id &&
            invoices.length === 0 &&
            !loadingInvoices && (
              <p className="text-xs text-gray-500 mt-1">
                No existing invoices found for this customer. A new invoice will
                be created automatically.
              </p>
            )}
          {entityType === "customer" && formData.invoice_id && (
            <p className="text-xs text-amber-600 mt-1">
              ⚠️ This cargo will be added as a line item to the selected
              invoice, updating the total amount.
            </p>
          )}
          {entityType === "supplier" && formData.supplier_id && (
            <p className="text-xs text-blue-600 mt-1">
              ℹ️ A new invoice will be created automatically for this supplier
              cargo. Supplier invoices are used for tracking and record-keeping
              purposes.
            </p>
          )}
        </div>
      </div>

      <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          className="px-4 py-2 text-sm border-gray-200 text-gray-700 hover:bg-gray-50 rounded-md"
          disabled={isSubmitting}
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary/90"
          disabled={isSubmitting || !formData.type}
        >
          {isSubmitting ? (
            <Loader2 size={16} className="animate-spin mr-2" />
          ) : null}
          {isSubmitting ? "Updating..." : "Update Cargo"}
        </Button>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Cargo Update</DialogTitle>
            <DialogDescription>
              This update will affect the following entities:
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-3">
            {pendingSubmitData && (
              <>
                {/* Invoice Changes */}
                {pendingSubmitData.invoice_id !== cargo?.invoice_id && (
                  <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <h4 className="font-medium text-amber-800 mb-1">
                      📄 Invoice Assignment
                    </h4>
                    <p className="text-sm text-amber-700">
                      {cargo?.invoice_id
                        ? `Moving from Invoice ${cargo.invoice_id}`
                        : "Adding to invoice"}
                      {pendingSubmitData.invoice_id
                        ? ` to Invoice ${pendingSubmitData.invoice_id}`
                        : " (removing from current invoice)"}
                    </p>
                    <p className="text-xs text-amber-600 mt-1">
                      This will update invoice totals and line items.
                    </p>
                  </div>
                )}

                {/* Batch Changes */}
                {pendingSubmitData.batch_id !== cargo?.batch_id && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-1">
                      📦 Batch Assignment
                    </h4>
                    <p className="text-sm text-blue-700">
                      {cargo?.batch_id
                        ? `Moving from ${batches.find((b) => b.id === cargo.batch_id)?.name || "Unknown Batch"}`
                        : "Adding to batch"}
                      {pendingSubmitData.batch_id
                        ? ` to ${batches.find((b) => b.id === pendingSubmitData.batch_id)?.name || "Unknown Batch"}`
                        : " (removing from current batch)"}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      This will update batch contents and status tracking.
                    </p>
                  </div>
                )}

                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-medium text-green-800 mb-1">
                    📝 Tracking History
                  </h4>
                  <p className="text-sm text-green-700">
                    All changes will be logged to the cargo's tracking history
                    for audit purposes.
                  </p>
                </div>
              </>
            )}
          </div>

          <DialogFooter className="mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancelUpdate}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleConfirmUpdate}
              disabled={isSubmitting}
              className="bg-primary text-white hover:bg-primary/90"
            >
              {isSubmitting ? (
                <Loader2 size={16} className="animate-spin mr-2" />
              ) : null}
              {isSubmitting ? "Updating..." : "Confirm Update"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </form>
  );
};
