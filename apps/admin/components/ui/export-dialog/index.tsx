"use client";

import { useState, useCallback, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { But<PERSON> } from "@workspace/ui/components/button";

import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import { Listing } from "@/modules/listing";
import {
  FileSpreadsheet,
  FileText,
  Download,
  Loader2,
  CheckCircle2,
  AlertCircle,
  Eye,
  X,
  Settings,
} from "lucide-react";
import { useExcelExport } from "@/lib/excel/useExcelExport";
import { generatePDFFromJSON } from "@workspace/pdf-generator";
import type { JSONDocumentData, PDFOptions } from "@workspace/pdf-generator";
import { showToast } from "@/lib/utils";

export interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  data: Record<string, any>[];
  title?: string;
  filename?: string;
  excludeColumns?: string[];
  columnMapping?: Record<string, string>;
  onSuccess?: (type: "excel" | "pdf", filename: string) => void;
  onError?: (error: string) => void;
}

type ExportStatus = "idle" | "loading" | "success" | "error";

interface ExportState {
  excel: ExportStatus;
  pdf: ExportStatus;
}

export function ExportDialog({
  isOpen,
  onClose,
  data,
  title = "Export Data",
  filename,
  excludeColumns,
  columnMapping,
  onSuccess,
  onError,
}: ExportDialogProps) {
  const [exportState, setExportState] = useState<ExportState>({
    excel: "idle",
    pdf: "idle",
  });
  const [previewData, setPreviewData] = useState<Record<string, any>[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [customFilename, setCustomFilename] = useState<string>("");
  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [showColumnSelector, setShowColumnSelector] = useState(false);

  const excel = useExcelExport({
    autoDownload: false,
    onSuccess: () => {
      setExportState((prev) => ({ ...prev, excel: "success" }));
      showToast("Excel export completed successfully!");
      if (onSuccess) onSuccess("excel", getFilename("xlsx"));
    },
    onError: (error) => {
      setExportState((prev) => ({ ...prev, excel: "error" }));
      showToast(error, "error");
      if (onError) onError(error);
    },
  });

  const getFilename = useCallback(
    (extension: string) => {
      const baseFilename =
        customFilename ||
        filename ||
        `export_${new Date().toISOString().split("T")[0]}`;
      return baseFilename.includes(".")
        ? baseFilename.replace(/\.[^/.]+$/, `.${extension}`)
        : `${baseFilename}.${extension}`;
    },
    [customFilename, filename]
  );

  // Track if columns have been manually modified
  const [columnsInitialized, setColumnsInitialized] = useState(false);

  // Initialize available columns and selected columns when data changes
  useEffect(() => {
    console.log("Columns Mapping: ", columnMapping);

    if (data && data.length > 0) {
      const allColumns = Object.keys(data[0] || {});

      const filteredColumns = allColumns.filter((col) => {
        // Remove excluded columns and 'id' column
        if (excludeColumns?.includes(col) || col === "id") return false;

        // Remove ALL entity ID columns (any column ending with 'Id')
        if (col.endsWith("Id") || col === "entityType") {
          console.log(`Filtering out entity ID column: ${col}`);
          return false;
        }

        return true;
      });

      setAvailableColumns(filteredColumns);

      // Initialize selected columns with all available columns only on first load
      if (!columnsInitialized) {
        setSelectedColumns(filteredColumns);
        setColumnsInitialized(true);
      }

      // Initialize custom filename if not set
      if (!customFilename && filename) {
        setCustomFilename(filename.replace(/\.[^/.]+$/, ""));
      }
    }
  }, [data, excludeColumns, customFilename, filename, columnsInitialized]);

  // Helper function to transform entity ID columns to entity names
  const transformEntityIdToName = useCallback(
    (key: string, value: any, item: any) => {
      // Skip if value is null/undefined
      if (value === null || value === undefined) return "";

      // Check if this is an entity ID column (ends with 'Id')
      if (key.endsWith("Id")) {
        const entityType = key.replace("Id", "").toLowerCase();

        // Try to find the corresponding entity name in the item
        // Common patterns: customer -> customer.name, batch -> batch.code, freight -> freight.name
        const entityKey = entityType;
        const entityPluralKey = entityType + "s";

        // Check for nested entity object
        if (item[entityKey] && typeof item[entityKey] === "object") {
          return (
            item[entityKey].name ||
            item[entityKey].code ||
            item[entityKey].tracking_number ||
            value
          );
        }

        // Check for plural nested entity object
        if (
          item[entityPluralKey] &&
          typeof item[entityPluralKey] === "object"
        ) {
          return (
            item[entityPluralKey].name ||
            item[entityPluralKey].code ||
            item[entityPluralKey].tracking_number ||
            value
          );
        }

        // Check for direct name fields (e.g., customer_name, code)
        const nameField = entityType + "_name";
        const codeField = entityType + "_code";
        const trackingField = entityType + "_tracking_number";

        if (item[nameField]) return item[nameField];
        if (item[codeField]) return item[codeField];
        if (item[trackingField]) return item[trackingField];
      }

      return value;
    },
    []
  );

  const processedData = useCallback(() => {
    if (!data || data.length === 0) return [];

    return data.map((item) => {
      const processed: Record<string, any> = {};

      // Only process columns that are in availableColumns and selectedColumns
      const columnsToProcess =
        selectedColumns.length > 0
          ? selectedColumns.filter((col) => availableColumns.includes(col))
          : availableColumns;

      columnsToProcess.forEach((key) => {
        const value = item[key];

        // Apply column mapping
        const displayKey = columnMapping?.[key] || key;

        // Transform entity IDs to entity names (for any remaining ID columns that weren't filtered)
        const transformedValue = transformEntityIdToName(key, value, item);

        // Format values for display
        if (transformedValue === null || transformedValue === undefined) {
          processed[displayKey] = "";
        } else if (
          typeof transformedValue === "object" &&
          transformedValue instanceof Date
        ) {
          processed[displayKey] = transformedValue.toLocaleDateString();
        } else if (typeof transformedValue === "object") {
          processed[displayKey] = JSON.stringify(transformedValue);
        } else {
          processed[displayKey] = transformedValue;
        }
      });

      return processed;
    });
  }, [
    data,
    availableColumns,
    columnMapping,
    selectedColumns,
    transformEntityIdToName,
  ]);

  const handleExcelExport = useCallback(async () => {
    if (!data || data.length === 0) {
      showToast("No data to export", "error");
      return;
    }

    // Check if any columns are selected
    if (selectedColumns.length === 0) {
      showToast("Please select at least one column to export", "error");
      return;
    }

    setExportState((prev) => ({ ...prev, excel: "loading" }));

    try {
      // Use processedData() to respect user column selection, just like PDF export
      const processed = processedData();

      if (processed.length === 0) {
        throw new Error("No data to export after processing");
      }

      // Pass the processed data instead of raw data
      // This ensures user column selection is respected
      const result = await excel.exportJSON(processed, {
        filename: getFilename("xlsx"),
        // Don't use excludeColumns since processedData() already handles column filtering
        // Don't use columnMapping since processedData() already applies column transformations
        theme: "DEFAULT",
      });

      if (result && excel.lastExport?.success && excel.lastExport.data) {
        excel.downloadLastExport();
      }
    } catch (error) {
      setExportState((prev) => ({ ...prev, excel: "error" }));
      const errorMessage =
        error instanceof Error ? error.message : "Excel export failed";
      showToast(errorMessage, "error");
      if (onError) onError(errorMessage);
    }
  }, [data, excel, getFilename, onError, selectedColumns, processedData]);

  const handlePDFExport = useCallback(async () => {
    if (!data || data.length === 0) {
      showToast("No data to export", "error");
      return;
    }

    // Check column limit for PDF export
    if (selectedColumns.length > 12) {
      showToast(
        "PDF export supports a maximum of 12 columns. Please deselect some columns or use Excel export for more columns.",
        "error"
      );
      return;
    }

    setExportState((prev) => ({ ...prev, pdf: "loading" }));

    try {
      const processed = processedData();

      if (processed.length === 0) {
        throw new Error("No data to export after processing");
      }

      // Get headers from first row
      const headers =
        processed.length > 0 ? Object.keys(processed[0] || {}) : [];

      // Additional check after processing (in case of edge cases)
      if (headers.length > 12) {
        throw new Error(
          "Too many columns for PDF export. Maximum 12 columns allowed."
        );
      }

      // Convert data to table format
      const tableData = processed.map((row) =>
        headers.map((header) => row[header] || "")
      );

      // Create PDF content
      const pdfContent: JSONDocumentData = {
        title: title,
        metadata: {
          author: "Shamwaa Logistics System",
          subject: `${title} Export`,
          keywords: "export,data,table",
          creator: "Shamwaa Logistics",
        },
        content: [
          {
            type: "heading",
            value: title,
            style: {
              fontSize: 18,
              bold: true,
              alignment: "center",
            },
          },
          {
            type: "paragraph",
            value: `Generated on: ${new Date().toLocaleDateString()}`,
            style: {
              fontSize: 10,
              alignment: "center",
              color: "#666666",
            },
          },
          {
            type: "table",
            value: {
              head: headers,
              body: tableData,
            },
            style: {
              fontSize: 9,
            },
          },
          {
            type: "paragraph",
            value: `Total records: ${processed.length}`,
            style: {
              fontSize: 10,
              alignment: "right",
              color: "#666666",
            },
          },
        ],
      };

      const pdfOptions: PDFOptions = {
        format: "a4",
        orientation: (tableData[0]?.length || 0) > 6 ? "landscape" : "portrait",
        fontName: "helvetica",
        fontSize: 10,
        margins: { top: 20, right: 15, bottom: 20, left: 15 },
        showHeader: true,
        headerText: title,
        showFooter: true,
        footerText: `Generated by Shamwaa Logistics | ${new Date().toLocaleDateString()}`,
        includePageNumbers: true,
      };

      const pdfBlob = await generatePDFFromJSON(pdfContent, pdfOptions);

      // Download PDF
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = getFilename("pdf");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setExportState((prev) => ({ ...prev, pdf: "success" }));
      showToast("PDF export completed successfully!");
      if (onSuccess) onSuccess("pdf", getFilename("pdf"));
    } catch (error) {
      setExportState((prev) => ({ ...prev, pdf: "error" }));
      const errorMessage =
        error instanceof Error ? error.message : "PDF export failed";
      showToast(errorMessage, "error");
      if (onError) onError(errorMessage);
    }
  }, [
    data,
    processedData,
    title,
    getFilename,
    onSuccess,
    onError,
    selectedColumns,
  ]);

  const handlePreview = useCallback(() => {
    const processed = processedData();
    setPreviewData(processed.slice(0, 10)); // Show first 10 rows
    setShowPreview(true);
  }, [processedData]);

  const handleColumnToggle = useCallback((column: string) => {
    setSelectedColumns((prev) =>
      prev.includes(column)
        ? prev.filter((col) => col !== column)
        : [...prev, column]
    );
  }, []);

  const handleSelectAllColumns = useCallback(() => {
    setSelectedColumns(availableColumns);
  }, [availableColumns]);

  const handleDeselectAllColumns = useCallback(() => {
    setSelectedColumns([]);
  }, []);

  const handleClose = useCallback(() => {
    setExportState({ excel: "idle", pdf: "idle" });
    setShowPreview(false);
    setPreviewData([]);
    setShowColumnSelector(false);
    setCustomFilename("");
    setSelectedColumns([]);
    setAvailableColumns([]);
    setColumnsInitialized(false);
    onClose();
  }, [onClose]);

  const getStatusIcon = (status: ExportStatus) => {
    switch (status) {
      case "loading":
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case "success":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  if (!data || data.length === 0) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              No Data Available
            </DialogTitle>
          </DialogHeader>
          <div className="py-6 text-center">
            <p className="text-gray-600">No data available to export.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5 text-primary" />
            Select File Type to Export
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6 max-h-[45em] overflow-y-scroll"
        >
          {/* Data Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">{title}</h3>
                <p className="text-sm text-gray-600">
                  {data.length} record{data.length !== 1 ? "s" : ""} ready for
                  export
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreview}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                Preview
              </Button>
            </div>
          </div>

          {/* Filename Input */}
          <div className="space-y-2">
            <Label htmlFor="filename" className="text-sm font-medium">
              Filename
            </Label>
            <Input
              id="filename"
              value={customFilename}
              onChange={(e) => setCustomFilename(e.target.value)}
              placeholder={
                filename || `export_${new Date().toISOString().split("T")[0]}`
              }
              className="w-full"
            />
            <p className="text-xs text-gray-500">
              File extension will be added automatically based on export type
            </p>
          </div>

          {/* Column Selector */}
          <Collapsible
            open={showColumnSelector}
            onOpenChange={setShowColumnSelector}
          >
            <CollapsibleTrigger asChild>
              <Button variant="outline" className="w-full justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Select Columns ({selectedColumns.length} of{" "}
                  {availableColumns.length})
                </div>
                <motion.div
                  animate={{ rotate: showColumnSelector ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <X className="h-4 w-4" />
                </motion.div>
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-3 pt-3">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAllColumns}
                  className="flex-1"
                >
                  Select All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAllColumns}
                  className="flex-1"
                >
                  Deselect All
                </Button>
              </div>
              <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto p-2 border rounded-lg bg-gray-50">
                {availableColumns.map((column) => {
                  // Apply the same transformation logic as processedData
                  const displayLabel = columnMapping?.[column];

                  return (
                    <div key={column} className="flex items-center space-x-2">
                      <Checkbox
                        id={`column-${column}`}
                        checked={selectedColumns.includes(column)}
                        onCheckedChange={() => handleColumnToggle(column)}
                      />
                      <Label
                        htmlFor={`column-${column}`}
                        className="text-sm cursor-pointer flex-1 truncate"
                        title={displayLabel}
                      >
                        {displayLabel}
                      </Label>
                    </div>
                  );
                })}
              </div>
              {selectedColumns.length === 0 && (
                <div className="text-center py-2 text-sm text-amber-600 bg-amber-50 rounded-lg">
                  <AlertCircle className="h-4 w-4 inline mr-1" />
                  Please select at least one column to export
                </div>
              )}
              {selectedColumns.length > 12 && (
                <div className="text-center py-2 text-sm text-red-600 bg-red-50 rounded-lg">
                  <AlertCircle className="h-4 w-4 inline mr-1" />
                  PDF export supports maximum 12 columns. Currently selected:{" "}
                  {selectedColumns.length}
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Excel Export */}
            <motion.div
              whileHover={{ scale: selectedColumns.length === 0 ? 1 : 1.02 }}
              whileTap={{ scale: selectedColumns.length === 0 ? 1 : 0.98 }}
              className={`border rounded-lg p-6 transition-colors ${
                selectedColumns.length === 0
                  ? "opacity-50 cursor-not-allowed border-amber-200"
                  : "cursor-pointer hover:border-primary/50"
              }`}
              onClick={
                exportState.excel === "loading" || selectedColumns.length === 0
                  ? undefined
                  : handleExcelExport
              }
            >
              <div className="flex items-center gap-3 mb-3">
                <div
                  className={`p-2 rounded-lg ${
                    selectedColumns.length === 0
                      ? "bg-amber-100"
                      : "bg-green-100"
                  }`}
                >
                  <FileSpreadsheet
                    className={`h-6 w-6 ${
                      selectedColumns.length === 0
                        ? "text-amber-600"
                        : "text-green-600"
                    }`}
                  />
                </div>
                <div className="flex-1">
                  <h3
                    className={`font-semibold ${
                      selectedColumns.length === 0 ? "text-gray-400" : ""
                    }`}
                  >
                    Excel (.xlsx)
                  </h3>
                  <p
                    className={`text-sm ${
                      selectedColumns.length === 0
                        ? "text-gray-400"
                        : "text-gray-600"
                    }`}
                  >
                    Spreadsheet format
                  </p>
                </div>
                {getStatusIcon(exportState.excel)}
              </div>
              <p
                className={`text-xs mb-3 ${
                  selectedColumns.length === 0
                    ? "text-amber-600"
                    : "text-gray-500"
                }`}
              >
                {selectedColumns.length === 0
                  ? "Please select at least one column to export"
                  : "Perfect for data analysis, calculations, and further processing"}
              </p>
            </motion.div>

            {/* PDF Export */}
            <motion.div
              whileHover={{ scale: selectedColumns.length > 12 ? 1 : 1.02 }}
              whileTap={{ scale: selectedColumns.length > 12 ? 1 : 0.98 }}
              className={`border rounded-lg p-6 transition-colors ${
                selectedColumns.length === 0 || selectedColumns.length > 12
                  ? "opacity-50 cursor-not-allowed border-red-200"
                  : "cursor-pointer hover:border-primary/50"
              }`}
              onClick={
                exportState.pdf === "loading" ||
                selectedColumns.length === 0 ||
                selectedColumns.length > 12
                  ? undefined
                  : handlePDFExport
              }
            >
              <div className="flex items-center gap-3 mb-3">
                <div
                  className={`p-2 rounded-lg ${
                    selectedColumns.length > 12 ? "bg-red-200" : "bg-red-100"
                  }`}
                >
                  <FileText
                    className={`h-6 w-6 ${
                      selectedColumns.length > 12
                        ? "text-red-400"
                        : "text-red-600"
                    }`}
                  />
                </div>
                <div className="flex-1">
                  <h3
                    className={`font-semibold ${
                      selectedColumns.length > 12 ? "text-gray-400" : ""
                    }`}
                  >
                    PDF (.pdf)
                  </h3>
                  <p
                    className={`text-sm ${
                      selectedColumns.length > 12
                        ? "text-gray-400"
                        : "text-gray-600"
                    }`}
                  >
                    Document format
                  </p>
                </div>
                {getStatusIcon(exportState.pdf)}
              </div>
              <p
                className={`text-xs mb-3 ${
                  selectedColumns.length > 12
                    ? "text-gray-400"
                    : "text-gray-500"
                }`}
              >
                {selectedColumns.length > 12
                  ? `Too many columns selected (${selectedColumns.length}/12 max)`
                  : "Ideal for sharing, printing, and archival purposes"}
              </p>
            </motion.div>
          </div>

          {/* Preview Modal */}
          {showPreview && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="w-full max-w-4xl border rounded-lg overflow-x-scroll"
            >
              <div className="bg-gray-50 px-4 py-2 flex items-center justify-between">
                <h4 className="font-medium text-sm">
                  Data Preview (First {previewData.length} row
                  {previewData.length > 1 ? "s" : ""})
                </h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPreview(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <Listing.Table
                data={previewData}
                pagination={{
                  currentPage: 1,
                  totalPages: 1,
                  totalItems: previewData.length,
                  itemsPerPage: 10,
                  onPageChange: () => {},
                }}
                columns={
                  previewData.length > 0
                    ? Object.keys(previewData[0] || {}).map((key) => ({
                        key,
                        label: key,
                        render: (item: any) => {
                          const value = String(item[key] || "");
                          return value.length > 30
                            ? `${value.substring(0, 30)}...`
                            : value;
                        },
                        className: "text-xs",
                      }))
                    : []
                }
                loading={false}
                className="border-0"
              />
            </motion.div>
          )}
        </motion.div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
